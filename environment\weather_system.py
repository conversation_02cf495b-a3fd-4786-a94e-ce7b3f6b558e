#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de clima avanzado para la simulación Universe 25.

Proporciona condiciones climáticas dinámicas que afectan el comportamiento
de los Nexus, la disponibilidad de recursos y la supervivencia.
"""

import pygame
import random
import math
import time
from collections import defaultdict

class WeatherSystem:
    """Sistema de clima dinámico y realista."""
    
    def __init__(self, config):
        """Inicializa el sistema de clima.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Estado actual del clima
        self.current_weather = "clear"
        self.temperature = 20.0  # Celsius
        self.humidity = 50.0     # Porcentaje
        self.wind_speed = 5.0    # km/h
        self.wind_direction = 0.0  # Radianes
        self.pressure = 1013.25  # hPa
        
        # Tipos de clima disponibles
        self.weather_types = {
            "clear": {
                "name": "Despejado",
                "temp_range": (15, 30),
                "humidity_range": (30, 60),
                "wind_range": (0, 15),
                "pressure_range": (1010, 1025),
                "duration_range": (300, 600),  # frames
                "color_filter": (255, 255, 255, 0),
                "visibility": 1.0,
                "resource_modifier": 1.0,
                "stress_modifier": 0.0
            },
            "cloudy": {
                "name": "Nublado",
                "temp_range": (10, 25),
                "humidity_range": (50, 80),
                "wind_range": (5, 20),
                "pressure_range": (1005, 1020),
                "duration_range": (200, 500),
                "color_filter": (200, 200, 220, 50),
                "visibility": 0.8,
                "resource_modifier": 0.9,
                "stress_modifier": 0.1
            },
            "rain": {
                "name": "Lluvia",
                "temp_range": (5, 20),
                "humidity_range": (80, 100),
                "wind_range": (10, 30),
                "pressure_range": (995, 1010),
                "duration_range": (100, 300),
                "color_filter": (150, 150, 200, 100),
                "visibility": 0.6,
                "resource_modifier": 1.5,  # Más agua disponible
                "stress_modifier": 0.3
            },
            "storm": {
                "name": "Tormenta",
                "temp_range": (0, 15),
                "humidity_range": (90, 100),
                "wind_range": (30, 60),
                "pressure_range": (980, 1000),
                "duration_range": (50, 150),
                "color_filter": (100, 100, 150, 150),
                "visibility": 0.3,
                "resource_modifier": 0.5,
                "stress_modifier": 0.8
            },
            "snow": {
                "name": "Nieve",
                "temp_range": (-10, 5),
                "humidity_range": (70, 90),
                "wind_range": (5, 25),
                "pressure_range": (1000, 1020),
                "duration_range": (200, 400),
                "color_filter": (220, 220, 255, 80),
                "visibility": 0.5,
                "resource_modifier": 0.3,
                "stress_modifier": 0.6
            },
            "fog": {
                "name": "Niebla",
                "temp_range": (5, 15),
                "humidity_range": (90, 100),
                "wind_range": (0, 5),
                "pressure_range": (1005, 1015),
                "duration_range": (150, 350),
                "color_filter": (180, 180, 180, 120),
                "visibility": 0.2,
                "resource_modifier": 0.8,
                "stress_modifier": 0.4
            },
            "hot": {
                "name": "Calor extremo",
                "temp_range": (35, 50),
                "humidity_range": (20, 40),
                "wind_range": (0, 10),
                "pressure_range": (1015, 1030),
                "duration_range": (400, 800),
                "color_filter": (255, 200, 150, 60),
                "visibility": 0.9,
                "resource_modifier": 0.6,  # Menos agua
                "stress_modifier": 0.5
            }
        }
        
        # Estado del sistema
        self.weather_timer = 0
        self.weather_duration = 300
        self.transition_timer = 0
        self.transitioning = False
        self.next_weather = None
        
        # Efectos visuales
        self.rain_drops = []
        self.snow_flakes = []
        self.fog_particles = []
        self.lightning_timer = 0
        
        # Estaciones del año
        self.season = "spring"
        self.season_timer = 0
        self.season_duration = 2000  # frames por estación
        
        # Inicializar clima
        self._initialize_weather()
    
    def _initialize_weather(self):
        """Inicializa el clima con valores aleatorios."""
        self.current_weather = random.choice(["clear", "cloudy"])
        self._update_weather_parameters()
        self.weather_duration = random.randint(*self.weather_types[self.current_weather]["duration_range"])
    
    def _update_weather_parameters(self):
        """Actualiza los parámetros del clima actual."""
        weather_data = self.weather_types[self.current_weather]
        
        self.temperature = random.uniform(*weather_data["temp_range"])
        self.humidity = random.uniform(*weather_data["humidity_range"])
        self.wind_speed = random.uniform(*weather_data["wind_range"])
        self.wind_direction = random.uniform(0, 2 * math.pi)
        self.pressure = random.uniform(*weather_data["pressure_range"])
    
    def update(self, world):
        """Actualiza el sistema de clima.
        
        Args:
            world (World): Mundo de la simulación.
        """
        # Actualizar estación
        self._update_season()
        
        # Actualizar clima
        self._update_weather()
        
        # Actualizar efectos visuales
        self._update_visual_effects(world)
        
        # Aplicar efectos del clima a los Nexus
        self._apply_weather_effects(world)
        
        # Actualizar recursos según el clima
        self._update_resource_availability(world)
    
    def _update_season(self):
        """Actualiza la estación del año."""
        self.season_timer += 1
        
        if self.season_timer >= self.season_duration:
            self.season_timer = 0
            seasons = ["spring", "summer", "autumn", "winter"]
            current_index = seasons.index(self.season)
            self.season = seasons[(current_index + 1) % 4]
    
    def _update_weather(self):
        """Actualiza el estado del clima."""
        self.weather_timer += 1
        
        # Verificar si es hora de cambiar el clima
        if self.weather_timer >= self.weather_duration and not self.transitioning:
            self._start_weather_transition()
        
        # Procesar transición
        if self.transitioning:
            self.transition_timer += 1
            if self.transition_timer >= 60:  # 1 segundo de transición
                self._complete_weather_transition()
        
        # Variaciones menores en los parámetros
        self._apply_weather_variations()
    
    def _start_weather_transition(self):
        """Inicia una transición de clima."""
        self.transitioning = True
        self.transition_timer = 0
        
        # Seleccionar nuevo clima basado en la estación y clima actual
        self.next_weather = self._select_next_weather()
    
    def _select_next_weather(self):
        """Selecciona el próximo clima basado en probabilidades."""
        # Probabilidades por estación
        season_probabilities = {
            "spring": {
                "clear": 0.3, "cloudy": 0.3, "rain": 0.3, "storm": 0.1
            },
            "summer": {
                "clear": 0.4, "cloudy": 0.2, "hot": 0.3, "storm": 0.1
            },
            "autumn": {
                "clear": 0.2, "cloudy": 0.4, "rain": 0.3, "fog": 0.1
            },
            "winter": {
                "cloudy": 0.3, "snow": 0.3, "fog": 0.2, "clear": 0.2
            }
        }
        
        probabilities = season_probabilities.get(self.season, season_probabilities["spring"])
        
        # Evitar repetir el mismo clima
        if self.current_weather in probabilities:
            probabilities[self.current_weather] *= 0.3
        
        # Selección aleatoria ponderada
        weather_options = list(probabilities.keys())
        weights = list(probabilities.values())
        
        return random.choices(weather_options, weights=weights)[0]
    
    def _complete_weather_transition(self):
        """Completa la transición de clima."""
        self.current_weather = self.next_weather
        self.next_weather = None
        self.transitioning = False
        self.weather_timer = 0
        
        # Actualizar parámetros del nuevo clima
        self._update_weather_parameters()
        
        # Establecer nueva duración
        weather_data = self.weather_types[self.current_weather]
        self.weather_duration = random.randint(*weather_data["duration_range"])
    
    def _apply_weather_variations(self):
        """Aplica variaciones menores a los parámetros del clima."""
        # Pequeñas variaciones aleatorias
        self.temperature += random.uniform(-0.1, 0.1)
        self.humidity += random.uniform(-0.5, 0.5)
        self.wind_speed += random.uniform(-0.2, 0.2)
        self.wind_direction += random.uniform(-0.05, 0.05)
        self.pressure += random.uniform(-0.1, 0.1)
        
        # Mantener dentro de rangos razonables
        weather_data = self.weather_types[self.current_weather]
        temp_min, temp_max = weather_data["temp_range"]
        hum_min, hum_max = weather_data["humidity_range"]
        wind_min, wind_max = weather_data["wind_range"]
        press_min, press_max = weather_data["pressure_range"]
        
        self.temperature = max(temp_min, min(temp_max, self.temperature))
        self.humidity = max(hum_min, min(hum_max, self.humidity))
        self.wind_speed = max(wind_min, min(wind_max, self.wind_speed))
        self.pressure = max(press_min, min(press_max, self.pressure))
    
    def _update_visual_effects(self, world):
        """Actualiza los efectos visuales del clima."""
        # Efectos de lluvia
        if self.current_weather == "rain" or self.current_weather == "storm":
            intensity = 5 if self.current_weather == "rain" else 15
            for _ in range(intensity):
                drop = {
                    "x": random.uniform(0, world.config.WORLD_SIZE[0]),
                    "y": random.uniform(-50, 0),
                    "speed": random.uniform(3, 8),
                    "size": random.randint(1, 3)
                }
                self.rain_drops.append(drop)
        
        # Actualizar gotas de lluvia
        for drop in list(self.rain_drops):
            drop["y"] += drop["speed"]
            if drop["y"] > world.config.WORLD_SIZE[1]:
                self.rain_drops.remove(drop)
        
        # Efectos de nieve
        if self.current_weather == "snow":
            for _ in range(3):
                flake = {
                    "x": random.uniform(0, world.config.WORLD_SIZE[0]),
                    "y": random.uniform(-50, 0),
                    "speed": random.uniform(1, 3),
                    "size": random.randint(2, 5),
                    "drift": random.uniform(-0.5, 0.5)
                }
                self.snow_flakes.append(flake)
        
        # Actualizar copos de nieve
        for flake in list(self.snow_flakes):
            flake["y"] += flake["speed"]
            flake["x"] += flake["drift"]
            if flake["y"] > world.config.WORLD_SIZE[1]:
                self.snow_flakes.remove(flake)
        
        # Efectos de tormenta (rayos)
        if self.current_weather == "storm":
            self.lightning_timer += 1
            if self.lightning_timer > random.randint(120, 300):  # 2-5 segundos
                self.lightning_timer = 0
                # Trigger lightning effect (implementar en renderizado)
    
    def _apply_weather_effects(self, world):
        """Aplica efectos del clima a los Nexus."""
        weather_data = self.weather_types[self.current_weather]
        stress_modifier = weather_data["stress_modifier"]
        
        for nexus in world.entities:
            if not nexus.alive:
                continue
            
            # Efectos en el estrés
            if hasattr(nexus, 'stress_level'):
                nexus.stress_level += stress_modifier * 0.001
                nexus.stress_level = max(0.0, min(1.0, nexus.stress_level))
            
            # Efectos en la energía
            if self.current_weather == "hot":
                nexus.energy -= 0.1  # Calor agota energía
            elif self.current_weather == "snow":
                nexus.energy -= 0.15  # Frío agota más energía
            elif self.current_weather == "storm":
                nexus.energy -= 0.05  # Estrés por tormenta
            
            # Efectos en la salud
            if self.current_weather == "rain" and random.random() < 0.001:
                nexus.health -= 1  # Posibilidad de enfermarse
            elif self.current_weather == "hot" and random.random() < 0.0005:
                nexus.health -= 2  # Golpe de calor
            
            # Modificar comportamiento
            if self.current_weather in ["storm", "snow"]:
                # Buscar refugio en clima severo
                if nexus.state not in ["seeking_shelter", "resting"]:
                    if random.random() < 0.1:
                        nexus.state = "seeking_shelter"
    
    def _update_resource_availability(self, world):
        """Actualiza la disponibilidad de recursos según el clima."""
        weather_data = self.weather_types[self.current_weather]
        modifier = weather_data["resource_modifier"]
        
        # Afectar regeneración de recursos
        if hasattr(world, 'resource_regeneration_rate'):
            world.resource_regeneration_rate = world.base_regeneration_rate * modifier
        
        # Crear recursos adicionales en lluvia
        if self.current_weather == "rain" and random.random() < 0.01:
            # Crear charcos de agua
            if len(world.resources) < world.config.MAX_RESOURCES:
                from environment.natural_elements import Resource
                water_resource = Resource(
                    [random.uniform(0, world.config.WORLD_SIZE[0]),
                     random.uniform(0, world.config.WORLD_SIZE[1])],
                    "water",
                    random.randint(5, 15)
                )
                world.resources.append(water_resource)
    
    def get_weather_info(self):
        """Obtiene información detallada del clima actual.
        
        Returns:
            dict: Información del clima.
        """
        weather_data = self.weather_types[self.current_weather]
        
        return {
            "type": self.current_weather,
            "name": weather_data["name"],
            "temperature": round(self.temperature, 1),
            "humidity": round(self.humidity, 1),
            "wind_speed": round(self.wind_speed, 1),
            "pressure": round(self.pressure, 1),
            "season": self.season,
            "visibility": weather_data["visibility"],
            "stress_factor": weather_data["stress_modifier"]
        }
    
    def render_weather_effects(self, screen, camera_offset, camera_zoom):
        """Renderiza efectos visuales del clima.
        
        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (list): Offset de la cámara.
            camera_zoom (float): Zoom de la cámara.
        """
        # Aplicar filtro de color del clima
        weather_data = self.weather_types[self.current_weather]
        color_filter = weather_data["color_filter"]
        
        if color_filter[3] > 0:  # Si hay alpha
            overlay = pygame.Surface(screen.get_size(), pygame.SRCALPHA)
            overlay.fill(color_filter)
            screen.blit(overlay, (0, 0))
        
        # Renderizar gotas de lluvia
        for drop in self.rain_drops:
            screen_x = (drop["x"] - camera_offset[0]) * camera_zoom
            screen_y = (drop["y"] - camera_offset[1]) * camera_zoom
            
            if 0 <= screen_x <= screen.get_width() and 0 <= screen_y <= screen.get_height():
                pygame.draw.line(screen, (150, 150, 255),
                               (screen_x, screen_y),
                               (screen_x, screen_y + drop["size"]), 1)
        
        # Renderizar copos de nieve
        for flake in self.snow_flakes:
            screen_x = (flake["x"] - camera_offset[0]) * camera_zoom
            screen_y = (flake["y"] - camera_offset[1]) * camera_zoom
            
            if 0 <= screen_x <= screen.get_width() and 0 <= screen_y <= screen.get_height():
                pygame.draw.circle(screen, (255, 255, 255),
                                 (int(screen_x), int(screen_y)), flake["size"])
    
    def force_weather(self, weather_type):
        """Fuerza un tipo de clima específico.
        
        Args:
            weather_type (str): Tipo de clima a establecer.
        """
        if weather_type in self.weather_types:
            self.current_weather = weather_type
            self._update_weather_parameters()
            self.weather_timer = 0
            weather_data = self.weather_types[weather_type]
            self.weather_duration = random.randint(*weather_data["duration_range"])
