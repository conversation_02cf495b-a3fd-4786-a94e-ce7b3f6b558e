#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de renderizado de mapas de calor para visualización de datos.

Proporciona mapas de calor para densidad de población, niveles de estrés,
actividad territorial y otros datos de la simulación Universe 25.
"""

import pygame
import math
from collections import defaultdict

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

class HeatmapRenderer:
    """Renderizador de mapas de calor para análisis visual."""
    
    def __init__(self, config):
        """Inicializa el renderizador de mapas de calor.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Configuración de mapas de calor
        self.grid_size = 50  # Tamaño de cada celda del grid
        self.grid_width = config.WORLD_SIZE[0] // self.grid_size
        self.grid_height = config.WORLD_SIZE[1] // self.grid_size
        
        # Datos de mapas de calor
        if NUMPY_AVAILABLE:
            self.population_heatmap = np.zeros((self.grid_height, self.grid_width))
            self.stress_heatmap = np.zeros((self.grid_height, self.grid_width))
            self.activity_heatmap = np.zeros((self.grid_height, self.grid_width))
            self.resource_heatmap = np.zeros((self.grid_height, self.grid_width))
            self.territory_heatmap = np.zeros((self.grid_height, self.grid_width))
        else:
            # Fallback usando listas de listas
            self.population_heatmap = [[0.0 for _ in range(self.grid_width)] for _ in range(self.grid_height)]
            self.stress_heatmap = [[0.0 for _ in range(self.grid_width)] for _ in range(self.grid_height)]
            self.activity_heatmap = [[0.0 for _ in range(self.grid_width)] for _ in range(self.grid_height)]
            self.resource_heatmap = [[0.0 for _ in range(self.grid_width)] for _ in range(self.grid_height)]
            self.territory_heatmap = [[0.0 for _ in range(self.grid_width)] for _ in range(self.grid_height)]
        
        # Configuración de colores
        self.color_schemes = {
            "population": {
                "low": (0, 0, 100),
                "medium": (100, 100, 255),
                "high": (255, 100, 100),
                "extreme": (255, 0, 0)
            },
            "stress": {
                "low": (0, 255, 0),
                "medium": (255, 255, 0),
                "high": (255, 100, 0),
                "extreme": (255, 0, 0)
            },
            "activity": {
                "low": (50, 50, 50),
                "medium": (100, 150, 100),
                "high": (150, 255, 150),
                "extreme": (255, 255, 255)
            },
            "resources": {
                "low": (100, 50, 0),
                "medium": (150, 100, 50),
                "high": (200, 150, 100),
                "extreme": (255, 200, 150)
            },
            "territory": {
                "low": (0, 0, 0),
                "medium": (100, 0, 100),
                "high": (200, 0, 200),
                "extreme": (255, 0, 255)
            }
        }
        
        # Estado del renderizador
        self.active_heatmap = None
        self.heatmap_alpha = 150
        self.update_frequency = 10  # Actualizar cada 10 frames
        self.frame_counter = 0
        
        # Superficies de cache
        self.heatmap_surfaces = {}
        self.needs_update = True
    
    def update(self, world):
        """Actualiza los datos de los mapas de calor.
        
        Args:
            world (World): Mundo de la simulación.
        """
        self.frame_counter += 1
        
        # Actualizar solo según la frecuencia configurada
        if self.frame_counter % self.update_frequency != 0:
            return
        
        # Limpiar mapas de calor
        if NUMPY_AVAILABLE:
            self.population_heatmap.fill(0)
            self.stress_heatmap.fill(0)
            self.activity_heatmap.fill(0)
            self.resource_heatmap.fill(0)
            self.territory_heatmap.fill(0)
        else:
            # Limpiar usando listas
            for y in range(self.grid_height):
                for x in range(self.grid_width):
                    self.population_heatmap[y][x] = 0.0
                    self.stress_heatmap[y][x] = 0.0
                    self.activity_heatmap[y][x] = 0.0
                    self.resource_heatmap[y][x] = 0.0
                    self.territory_heatmap[y][x] = 0.0
        
        # Actualizar mapa de población y estrés
        self._update_population_heatmap(world)
        
        # Actualizar mapa de actividad
        self._update_activity_heatmap(world)
        
        # Actualizar mapa de recursos
        self._update_resource_heatmap(world)
        
        # Actualizar mapa de territorio
        self._update_territory_heatmap(world)
        
        # Marcar para regenerar superficies
        self.needs_update = True
    
    def _update_population_heatmap(self, world):
        """Actualiza el mapa de calor de población y estrés."""
        for nexus in world.entities:
            if not nexus.alive:
                continue
            
            # Convertir posición a coordenadas de grid
            grid_x = int(nexus.position[0] // self.grid_size)
            grid_y = int(nexus.position[1] // self.grid_size)
            
            # Verificar límites
            if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
                # Incrementar densidad de población
                self.population_heatmap[grid_y, grid_x] += 1
                
                # Añadir nivel de estrés si está disponible
                if hasattr(nexus, 'stress_level'):
                    self.stress_heatmap[grid_y, grid_x] += nexus.stress_level
        
        # Normalizar mapa de estrés por población
        for y in range(self.grid_height):
            for x in range(self.grid_width):
                if self.population_heatmap[y, x] > 0:
                    self.stress_heatmap[y, x] /= self.population_heatmap[y, x]
    
    def _update_activity_heatmap(self, world):
        """Actualiza el mapa de calor de actividad."""
        activity_weights = {
            "idle": 0.1,
            "exploring": 0.8,
            "seeking_food": 0.6,
            "seeking_water": 0.6,
            "socializing": 0.7,
            "hunting": 0.9,
            "building": 1.0,
            "aggressive": 1.0,
            "competing": 0.9,
            "patrolling": 0.8
        }
        
        for nexus in world.entities:
            if not nexus.alive:
                continue
            
            grid_x = int(nexus.position[0] // self.grid_size)
            grid_y = int(nexus.position[1] // self.grid_size)
            
            if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
                activity_level = activity_weights.get(nexus.state, 0.5)
                self.activity_heatmap[grid_y, grid_x] += activity_level
    
    def _update_resource_heatmap(self, world):
        """Actualiza el mapa de calor de recursos."""
        for resource in world.resources:
            if resource.consumed:
                continue
            
            grid_x = int(resource.position[0] // self.grid_size)
            grid_y = int(resource.position[1] // self.grid_size)
            
            if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
                # Peso basado en el tipo de recurso
                weight = 1.0
                if hasattr(resource, 'type'):
                    if resource.type == "food":
                        weight = 1.5
                    elif resource.type == "water":
                        weight = 2.0
                
                self.resource_heatmap[grid_y, grid_x] += weight
    
    def _update_territory_heatmap(self, world):
        """Actualiza el mapa de calor de territorio."""
        if not hasattr(world, 'territory_system'):
            return
        
        for territory in world.territory_system.territories.values():
            center_x = territory.center[0]
            center_y = territory.center[1]
            radius = territory.size
            
            # Calcular área de influencia del territorio
            grid_radius = int(radius // self.grid_size) + 1
            center_grid_x = int(center_x // self.grid_size)
            center_grid_y = int(center_y // self.grid_size)
            
            for dy in range(-grid_radius, grid_radius + 1):
                for dx in range(-grid_radius, grid_radius + 1):
                    grid_x = center_grid_x + dx
                    grid_y = center_grid_y + dy
                    
                    if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
                        # Calcular distancia al centro del territorio
                        cell_center_x = (grid_x + 0.5) * self.grid_size
                        cell_center_y = (grid_y + 0.5) * self.grid_size
                        
                        distance = math.sqrt(
                            (cell_center_x - center_x)**2 + 
                            (cell_center_y - center_y)**2
                        )
                        
                        if distance <= radius:
                            # Intensidad basada en la distancia al centro
                            intensity = 1.0 - (distance / radius)
                            self.territory_heatmap[grid_y, grid_x] += intensity
    
    def render_heatmap(self, screen, heatmap_type, camera_offset, camera_zoom):
        """Renderiza un mapa de calor específico.
        
        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            heatmap_type (str): Tipo de mapa de calor.
            camera_offset (list): Offset de la cámara.
            camera_zoom (float): Zoom de la cámara.
        """
        if heatmap_type not in ["population", "stress", "activity", "resources", "territory"]:
            return
        
        # Obtener datos del mapa de calor
        if heatmap_type == "population":
            data = self.population_heatmap
        elif heatmap_type == "stress":
            data = self.stress_heatmap
        elif heatmap_type == "activity":
            data = self.activity_heatmap
        elif heatmap_type == "resources":
            data = self.resource_heatmap
        elif heatmap_type == "territory":
            data = self.territory_heatmap
        
        # Generar superficie si es necesario
        if self.needs_update or heatmap_type not in self.heatmap_surfaces:
            self.heatmap_surfaces[heatmap_type] = self._generate_heatmap_surface(data, heatmap_type)
        
        # Renderizar superficie
        surface = self.heatmap_surfaces[heatmap_type]
        if surface:
            self._render_heatmap_surface(screen, surface, camera_offset, camera_zoom)
        
        self.needs_update = False
    
    def _generate_heatmap_surface(self, data, heatmap_type):
        """Genera una superficie de mapa de calor.
        
        Args:
            data (np.array): Datos del mapa de calor.
            heatmap_type (str): Tipo de mapa de calor.
            
        Returns:
            pygame.Surface: Superficie del mapa de calor.
        """
        # Normalizar datos
        if NUMPY_AVAILABLE:
            max_value = np.max(data)
            if max_value == 0:
                return None
            normalized_data = data / max_value
        else:
            # Encontrar valor máximo manualmente
            max_value = 0
            for row in data:
                for value in row:
                    if value > max_value:
                        max_value = value

            if max_value == 0:
                return None

            # Normalizar manualmente
            normalized_data = []
            for row in data:
                normalized_row = [value / max_value for value in row]
                normalized_data.append(normalized_row)
        
        # Crear superficie
        surface = pygame.Surface(
            (self.grid_width * self.grid_size, self.grid_height * self.grid_size),
            pygame.SRCALPHA
        )
        
        # Esquema de colores
        colors = self.color_schemes[heatmap_type]
        
        # Renderizar cada celda
        for y in range(self.grid_height):
            for x in range(self.grid_width):
                intensity = normalized_data[y, x]
                
                if intensity > 0:
                    # Interpolar color basado en intensidad
                    color = self._interpolate_color(intensity, colors)
                    alpha = int(self.heatmap_alpha * intensity)
                    
                    # Crear superficie de celda con alpha
                    cell_surface = pygame.Surface((self.grid_size, self.grid_size), pygame.SRCALPHA)
                    cell_surface.fill((*color, alpha))
                    
                    surface.blit(cell_surface, (x * self.grid_size, y * self.grid_size))
        
        return surface
    
    def _interpolate_color(self, intensity, color_scheme):
        """Interpola color basado en la intensidad.
        
        Args:
            intensity (float): Intensidad (0-1).
            color_scheme (dict): Esquema de colores.
            
        Returns:
            tuple: Color RGB interpolado.
        """
        if intensity <= 0.25:
            # Interpolar entre low y medium
            t = intensity / 0.25
            return self._lerp_color(color_scheme["low"], color_scheme["medium"], t)
        elif intensity <= 0.5:
            # Interpolar entre medium y high
            t = (intensity - 0.25) / 0.25
            return self._lerp_color(color_scheme["medium"], color_scheme["high"], t)
        elif intensity <= 0.75:
            # Interpolar entre high y extreme
            t = (intensity - 0.5) / 0.25
            return self._lerp_color(color_scheme["high"], color_scheme["extreme"], t)
        else:
            # Usar color extreme
            return color_scheme["extreme"]
    
    def _lerp_color(self, color1, color2, t):
        """Interpolación lineal entre dos colores.
        
        Args:
            color1 (tuple): Color inicial.
            color2 (tuple): Color final.
            t (float): Factor de interpolación (0-1).
            
        Returns:
            tuple: Color interpolado.
        """
        return (
            int(color1[0] + (color2[0] - color1[0]) * t),
            int(color1[1] + (color2[1] - color1[1]) * t),
            int(color1[2] + (color2[2] - color1[2]) * t)
        )
    
    def _render_heatmap_surface(self, screen, surface, camera_offset, camera_zoom):
        """Renderiza una superficie de mapa de calor en pantalla.
        
        Args:
            screen (pygame.Surface): Superficie de destino.
            surface (pygame.Surface): Superficie del mapa de calor.
            camera_offset (list): Offset de la cámara.
            camera_zoom (float): Zoom de la cámara.
        """
        # Calcular posición y escala
        scaled_width = int(surface.get_width() * camera_zoom)
        scaled_height = int(surface.get_height() * camera_zoom)
        
        # Escalar superficie si es necesario
        if camera_zoom != 1.0:
            scaled_surface = pygame.transform.scale(surface, (scaled_width, scaled_height))
        else:
            scaled_surface = surface
        
        # Calcular posición en pantalla
        screen_x = -camera_offset[0] * camera_zoom
        screen_y = -camera_offset[1] * camera_zoom
        
        # Renderizar
        screen.blit(scaled_surface, (screen_x, screen_y))
    
    def set_heatmap_alpha(self, alpha):
        """Establece la transparencia de los mapas de calor.
        
        Args:
            alpha (int): Valor alpha (0-255).
        """
        self.heatmap_alpha = max(0, min(255, alpha))
        self.needs_update = True
    
    def toggle_heatmap(self, heatmap_type):
        """Activa/desactiva un tipo de mapa de calor.
        
        Args:
            heatmap_type (str): Tipo de mapa de calor.
        """
        if self.active_heatmap == heatmap_type:
            self.active_heatmap = None
        else:
            self.active_heatmap = heatmap_type
    
    def render_active_heatmap(self, screen, camera_offset, camera_zoom):
        """Renderiza el mapa de calor activo.
        
        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (list): Offset de la cámara.
            camera_zoom (float): Zoom de la cámara.
        """
        if self.active_heatmap:
            self.render_heatmap(screen, self.active_heatmap, camera_offset, camera_zoom)
    
    def render_heatmap_legend(self, screen, heatmap_type, x, y):
        """Renderiza la leyenda de un mapa de calor.
        
        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            heatmap_type (str): Tipo de mapa de calor.
            x (int): Posición X.
            y (int): Posición Y.
        """
        if heatmap_type not in self.color_schemes:
            return
        
        colors = self.color_schemes[heatmap_type]
        
        # Título
        title = heatmap_type.capitalize() + " Heatmap"
        title_surface = pygame.font.Font(None, 24).render(title, True, (255, 255, 255))
        screen.blit(title_surface, (x, y))
        
        # Barra de colores
        bar_width = 200
        bar_height = 20
        bar_y = y + 30
        
        for i in range(bar_width):
            intensity = i / bar_width
            color = self._interpolate_color(intensity, colors)
            pygame.draw.line(screen, color, (x + i, bar_y), (x + i, bar_y + bar_height))
        
        # Etiquetas
        labels = ["Low", "Medium", "High", "Extreme"]
        for i, label in enumerate(labels):
            label_x = x + (i * bar_width // 3)
            label_surface = pygame.font.Font(None, 16).render(label, True, (200, 200, 200))
            screen.blit(label_surface, (label_x, bar_y + bar_height + 5))
    
    def get_heatmap_data_at_position(self, position, heatmap_type):
        """Obtiene el valor del mapa de calor en una posición específica.
        
        Args:
            position (tuple): Posición (x, y).
            heatmap_type (str): Tipo de mapa de calor.
            
        Returns:
            float: Valor en la posición.
        """
        grid_x = int(position[0] // self.grid_size)
        grid_y = int(position[1] // self.grid_size)
        
        if not (0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height):
            return 0.0
        
        if heatmap_type == "population":
            return self.population_heatmap[grid_y, grid_x]
        elif heatmap_type == "stress":
            return self.stress_heatmap[grid_y, grid_x]
        elif heatmap_type == "activity":
            return self.activity_heatmap[grid_y, grid_x]
        elif heatmap_type == "resources":
            return self.resource_heatmap[grid_y, grid_x]
        elif heatmap_type == "territory":
            return self.territory_heatmap[grid_y, grid_x]
        
        return 0.0
