# Nexus - Universe 25 Autonomous Life Simulation

A comprehensive autonomous life simulation system inspired by <PERSON>'s Universe 25 experiment, featuring complete individual autonomy, complex survival mechanics, and emergent social behaviors.

## Overview

This simulation creates a self-sustaining ecosystem where each character operates with complete autonomy, making independent decisions based on their personality, needs, stress levels, and environmental conditions. The system is designed to observe emergent behaviors and social dynamics that arise naturally from individual interactions, similar to the phenomena observed in the Universe 25 experiment.

## Key Features

### 🧠 Complete Individual Autonomy
- **Advanced AI Decision-Making**: Each character uses sophisticated decision trees based on multiple factors
- **Personality-Driven Behavior**: Unique personality archetypes (Alpha, Beta, Omega, Explorer, Caretaker, Hermit)
- **Adaptive Learning**: Characters learn from experiences and adapt their behavior over time
- **Goal-Oriented Behavior**: Personal goals and long-term planning capabilities

### 🏠 Comprehensive Survival Mechanics
- **Resource Management**: Food, water, shelter, energy, and tool crafting systems
- **Health & Wellness**: Detailed health tracking including diseases, stress, and psychological states
- **Environmental Interaction**: Characters can modify their environment and create tools
- **Lifecycle Management**: Aging, reproduction, and natural population dynamics

### 🌍 Complex Social Dynamics
- **Social Hierarchies**: Alpha-Beta-Omega and dominance chain systems
- **Territory & Competition**: Territorial behavior and resource competition
- **Stress Psychology**: Universe 25-inspired stress mechanics and behavioral breakdown
- **Communication**: Advanced social interaction and knowledge sharing

### 📊 Universe 25 Behavioral Patterns
- **Beautiful Ones**: Individuals who withdraw socially and focus on grooming
- **Aggressive Dominants**: Hyperaggressive territorial behavior
- **Passive Withdrawn**: Complete social withdrawal and reduced activity
- **Hyperactive Wanderers**: Purposeless hyperactivity and exploration

### 🎯 Emergent Behaviors
- **Population Pressure**: Stress increases with overcrowding
- **Social Breakdown**: Observable collapse under population stress
- **Behavioral Adaptation**: Unexpected behaviors emerge from individual interactions
- **Environmental Modification**: Characters actively change their environment

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd vida
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the simulation**:
   ```bash
   python main.py
   ```

## System Requirements

- Python 3.8+
- pygame 2.5.2+
- numpy 1.26.0+
- Additional dependencies listed in `requirements.txt`

## Controls

### Basic Controls
- **Mouse Click**: Select individual characters
- **Space**: Pause/Resume simulation
- **ESC**: Exit simulation
- **Mouse Drag**: Pan camera
- **Mouse Wheel**: Zoom in/out

### Advanced Controls
- **T**: Toggle real-time mode
- **+/-**: Adjust simulation speed
- **2**: Switch to 2D view (hold)
- **3**: Return to 3D isometric view
- **J**: Make selected characters jump

### Interface
- **≡ Menu**: Access all simulation controls
- **Select Nexus**: Choose characters to observe
- **Follow Nexus**: Camera follows selected character
- **Teach**: Share knowledge with characters
- **Debug Info**: View detailed statistics

## Understanding the Simulation

### Character States
Characters can be in various states that reflect their current activity:
- **Idle**: Resting or waiting
- **Exploring**: Discovering new areas
- **Seeking Food/Water**: Searching for resources
- **Socializing**: Interacting with others
- **Building**: Constructing shelters or tools
- **Territorial**: Establishing or defending territory
- **Stressed**: Under psychological pressure
- **Withdrawing**: Socially retreating (Universe 25 behavior)

### Stress Indicators
Monitor the Universe 25 metrics panel for:
- **Population Stress**: Overall stress levels in the population
- **Social Breakdown**: Percentage of population under high stress
- **Anomalous Behaviors**: Emergence of Universe 25 patterns
- **Territory Conflicts**: Competition for space and resources

### Resource Types
- **Food**: Yellow circles, essential for energy
- **Water**: Blue circles, required for health
- **Wood**: Brown, used for construction and tools
- **Stone**: Gray, used for advanced tools and buildings
- **Medicine**: Green, heals diseases and injuries

## Configuration

The simulation can be customized through `utils/config.py`:

### Population Settings
```python
POPULATION_STRESS_THRESHOLD = 50    # Population where stress begins
OVERCROWDING_RADIUS = 100          # Radius for detecting overcrowding
STRESS_DECAY_RATE = 0.001          # Natural stress reduction rate
```

### Territory Settings
```python
TERRITORY_MIN_SIZE = 50            # Minimum territory radius
TERRITORY_MAX_SIZE = 300           # Maximum territory radius
TERRITORY_ESTABLISHMENT_ENERGY = 40 # Energy required to claim territory
```

### Behavioral Settings
```python
ANOMALOUS_BEHAVIOR_CHANCE = 0.1    # Probability of developing anomalous behaviors
SOCIAL_BREAKDOWN_THRESHOLD = 0.6   # Threshold for social collapse
BEAUTIFUL_ONES_CHANCE = 0.3        # Probability of "beautiful ones" behavior
```

## Architecture

### Core Systems
- **`nexus/entity.py`**: Base character class with autonomous behavior
- **`nexus/autonomous_ai.py`**: Advanced AI decision-making system
- **`nexus/stress_psychology.py`**: Universe 25 stress and behavioral patterns
- **`nexus/resource_management.py`**: Comprehensive resource and inventory system
- **`nexus/territory_system.py`**: Territorial behavior and social hierarchies

### Environment
- **`environment/world.py`**: Main world simulation and management
- **`environment/natural_elements.py`**: Environmental objects and resources
- **`society/`**: Social systems, tribes, and cultural evolution

### Interface
- **`ui/interface.py`**: User interface and Universe 25 metrics display
- **`rendering3d/`**: 3D isometric rendering system

## Observing Universe 25 Phenomena

### Early Stages (Low Population)
- Characters establish territories
- Social hierarchies form naturally
- Reproduction and expansion occur

### Growth Phase (Medium Population)
- Increased competition for resources
- Territory conflicts emerge
- Stress levels begin to rise

### Behavioral Sink (High Population)
- Social breakdown becomes visible
- Anomalous behaviors emerge:
  - **Beautiful Ones**: Withdraw from society, excessive grooming
  - **Aggressive Dominants**: Hyperaggressive territorial control
  - **Passive Withdrawn**: Complete social withdrawal
  - **Hyperactive Wanderers**: Purposeless constant movement

### Population Collapse
- Reproduction rates decline
- Social structures break down
- Population may stabilize at lower levels or continue declining

## Research Applications

This simulation can be used to study:
- **Population Dynamics**: Effects of density on behavior
- **Social Psychology**: Emergence of hierarchies and social breakdown
- **Behavioral Ecology**: Territory, competition, and resource management
- **Complex Systems**: Emergent behaviors from simple rules
- **Urban Planning**: Insights into overcrowding effects

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source. Please see the LICENSE file for details.

## Acknowledgments

- Inspired by John B. Calhoun's Universe 25 experiment
- Built with pygame for visualization
- Uses advanced AI techniques for autonomous behavior

## Support

For questions, issues, or contributions, please open an issue on the repository or contact the development team.

---

**Note**: This simulation is designed for research and educational purposes. The behaviors observed are emergent properties of the system and may not directly correlate with real-world phenomena, though they are inspired by documented research in behavioral ecology and social psychology.
