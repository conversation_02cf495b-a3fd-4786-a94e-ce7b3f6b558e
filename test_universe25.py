#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for Universe 25 enhanced simulation.

This script tests the new autonomous systems and verifies they work correctly.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all new modules can be imported."""
    print("Testing imports...")
    
    try:
        from nexus.stress_psychology import StressPsychologySystem
        from nexus.resource_management import ResourceManagementSystem
        from nexus.territory_system import TerritorySystem
        from nexus.autonomous_ai import AutonomousAI
        print("✓ All new modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_system_initialization():
    """Test that systems can be initialized."""
    print("\nTesting system initialization...")
    
    try:
        from utils.config import Config
        config = Config()
        
        from nexus.stress_psychology import StressPsychologySystem
        from nexus.resource_management import ResourceManagementSystem
        from nexus.territory_system import TerritorySystem
        from nexus.autonomous_ai import AutonomousAI
        
        stress_system = StressPsychologySystem(config)
        resource_system = ResourceManagementSystem(config)
        territory_system = TerritorySystem(config)
        ai_system = AutonomousAI(config)
        
        print("✓ All systems initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Initialization error: {e}")
        return False

def test_world_integration():
    """Test that the world can integrate the new systems."""
    print("\nTesting world integration...")
    
    try:
        from utils.config import Config
        from environment.world import World
        
        config = Config()
        world = World(config)
        
        # Check if new systems are present
        assert hasattr(world, 'stress_psychology_system'), "Missing stress psychology system"
        assert hasattr(world, 'resource_management_system'), "Missing resource management system"
        assert hasattr(world, 'territory_system'), "Missing territory system"
        assert hasattr(world, 'autonomous_ai_system'), "Missing autonomous AI system"
        
        print("✓ World integration successful")
        return True
    except Exception as e:
        print(f"✗ World integration error: {e}")
        return False

def test_entity_creation():
    """Test that entities can be created with new systems."""
    print("\nTesting entity creation...")
    
    try:
        from utils.config import Config
        from environment.world import World
        from nexus.articulated_entity import ArticulatedNexus
        
        config = Config()
        world = World(config)
        
        # Create a test entity
        nexus = ArticulatedNexus(
            "TestNexus",
            [100, 100],
            world,
            "male"
        )
        
        world.add_entity(nexus)
        
        print("✓ Entity creation successful")
        return True
    except Exception as e:
        print(f"✗ Entity creation error: {e}")
        return False

def test_stress_system():
    """Test stress psychology system functionality."""
    print("\nTesting stress psychology system...")
    
    try:
        from utils.config import Config
        from environment.world import World
        from nexus.articulated_entity import ArticulatedNexus
        
        config = Config()
        world = World(config)
        
        # Create test entities
        for i in range(5):
            nexus = ArticulatedNexus(
                f"TestNexus{i}",
                [100 + i * 10, 100 + i * 10],
                world,
                "male" if i % 2 == 0 else "female"
            )
            world.add_entity(nexus)
        
        # Update stress system
        world.stress_psychology_system.update(world)
        
        # Get population report
        report = world.stress_psychology_system.get_population_report()
        
        assert 'total_population' in report, "Missing population data"
        assert 'stress_distribution' in report, "Missing stress distribution"
        assert 'anomalous_behaviors' in report, "Missing anomalous behaviors"
        
        print("✓ Stress psychology system working")
        return True
    except Exception as e:
        print(f"✗ Stress system error: {e}")
        return False

def test_resource_system():
    """Test resource management system functionality."""
    print("\nTesting resource management system...")
    
    try:
        from utils.config import Config
        from environment.world import World
        from nexus.articulated_entity import ArticulatedNexus
        
        config = Config()
        world = World(config)
        
        # Create test entity
        nexus = ArticulatedNexus("TestNexus", [100, 100], world, "male")
        world.add_entity(nexus)
        
        # Initialize inventory
        world.resource_management_system.initialize_inventory(nexus)
        
        # Test resource collection
        success = world.resource_management_system.collect_resource(
            nexus, "food", 5, world
        )
        
        assert success, "Resource collection failed"
        assert nexus.inventory["resources"]["food"] == 5, "Resource not added to inventory"
        
        print("✓ Resource management system working")
        return True
    except Exception as e:
        print(f"✗ Resource system error: {e}")
        return False

def test_territory_system():
    """Test territory system functionality."""
    print("\nTesting territory system...")
    
    try:
        from utils.config import Config
        from environment.world import World
        from nexus.articulated_entity import ArticulatedNexus
        
        config = Config()
        world = World(config)
        
        # Create test entity
        nexus = ArticulatedNexus("TestNexus", [100, 100], world, "male")
        nexus.energy = 50  # Enough energy for territory
        nexus.personality = {"territoriality": 0.8}  # High territoriality
        world.add_entity(nexus)
        
        # Update territory system
        world.territory_system.update(world)
        
        print("✓ Territory system working")
        return True
    except Exception as e:
        print(f"✗ Territory system error: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("=" * 50)
    print("UNIVERSE 25 ENHANCED SIMULATION TESTS")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_system_initialization,
        test_world_integration,
        test_entity_creation,
        test_stress_system,
        test_resource_system,
        test_territory_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Universe 25 simulation is ready!")
        print("\nYou can now run the simulation with:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
