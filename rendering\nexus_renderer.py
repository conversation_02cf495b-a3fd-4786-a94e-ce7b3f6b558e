#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de renderizado avanzado para Nexus.

Proporciona renderizado detallado de los Nexus con sprites, animaciones,
indicadores de estado y efectos visuales.
"""

import pygame
import math
import random
from collections import defaultdict

class NexusRenderer:
    """Renderizador avanzado para entidades Nexus."""
    
    def __init__(self, config):
        """Inicializa el renderizador de Nexus.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Sprites y animaciones
        self.sprites = {}
        self.animations = {}
        self.animation_states = defaultdict(dict)
        
        # Colores para diferentes estados
        self.state_colors = {
            "idle": (100, 150, 200),
            "exploring": (150, 200, 100),
            "seeking_food": (255, 200, 100),
            "seeking_water": (100, 200, 255),
            "socializing": (200, 150, 255),
            "resting": (150, 150, 150),
            "hunting": (255, 100, 100),
            "fleeing": (255, 50, 50),
            "building": (200, 150, 100),
            "reproducing": (255, 150, 200),
            "teaching": (100, 255, 150),
            "learning": (150, 255, 100),
            "aggressive": (255, 0, 0),
            "withdrawing": (100, 100, 100),
            "stressed": (255, 100, 0),
            "grooming": (200, 200, 255),
            "patrolling": (150, 100, 200),
            "establishing_territory": (200, 100, 150)
        }
        
        # Colores para personalidades
        self.personality_colors = {
            "alpha": (255, 215, 0),      # Dorado
            "beta": (192, 192, 192),     # Plateado
            "omega": (139, 69, 19),      # Marrón
            "explorer": (0, 255, 127),   # Verde primavera
            "caretaker": (255, 182, 193), # Rosa claro
            "hermit": (75, 0, 130)       # Índigo
        }
        
        # Efectos visuales
        self.stress_particles = []
        self.emotion_bubbles = []
        self.territory_markers = {}
        
        # Inicializar sprites
        self._create_sprites()
    
    def _create_sprites(self):
        """Crea sprites básicos para los Nexus."""
        # Sprite básico del Nexus (círculo con detalles)
        base_size = 20
        
        # Crear sprite base
        sprite = pygame.Surface((base_size * 2, base_size * 2), pygame.SRCALPHA)
        
        # Cuerpo principal
        pygame.draw.circle(sprite, (150, 150, 150), (base_size, base_size), base_size)
        pygame.draw.circle(sprite, (100, 100, 100), (base_size, base_size), base_size, 2)
        
        # Ojos
        eye_size = 3
        pygame.draw.circle(sprite, (255, 255, 255), (base_size - 6, base_size - 4), eye_size)
        pygame.draw.circle(sprite, (255, 255, 255), (base_size + 6, base_size - 4), eye_size)
        pygame.draw.circle(sprite, (0, 0, 0), (base_size - 6, base_size - 4), eye_size - 1)
        pygame.draw.circle(sprite, (0, 0, 0), (base_size + 6, base_size - 4), eye_size - 1)
        
        self.sprites["base"] = sprite
        
        # Crear variaciones para diferentes estados
        self._create_state_sprites(base_size)
    
    def _create_state_sprites(self, base_size):
        """Crea sprites para diferentes estados."""
        # Sprite para estado agresivo
        aggressive_sprite = pygame.Surface((base_size * 2, base_size * 2), pygame.SRCALPHA)
        pygame.draw.circle(aggressive_sprite, (255, 100, 100), (base_size, base_size), base_size)
        pygame.draw.circle(aggressive_sprite, (200, 0, 0), (base_size, base_size), base_size, 3)
        # Ojos rojos
        pygame.draw.circle(aggressive_sprite, (255, 0, 0), (base_size - 6, base_size - 4), 3)
        pygame.draw.circle(aggressive_sprite, (255, 0, 0), (base_size + 6, base_size - 4), 3)
        self.sprites["aggressive"] = aggressive_sprite
        
        # Sprite para estado estresado
        stressed_sprite = pygame.Surface((base_size * 2, base_size * 2), pygame.SRCALPHA)
        pygame.draw.circle(stressed_sprite, (255, 150, 0), (base_size, base_size), base_size)
        pygame.draw.circle(stressed_sprite, (255, 100, 0), (base_size, base_size), base_size, 2)
        # Líneas de estrés
        for i in range(4):
            angle = i * math.pi / 2
            start_x = base_size + math.cos(angle) * (base_size - 5)
            start_y = base_size + math.sin(angle) * (base_size - 5)
            end_x = base_size + math.cos(angle) * (base_size + 5)
            end_y = base_size + math.sin(angle) * (base_size + 5)
            pygame.draw.line(stressed_sprite, (255, 0, 0), (start_x, start_y), (end_x, end_y), 2)
        self.sprites["stressed"] = stressed_sprite
        
        # Sprite para estado retraído
        withdrawn_sprite = pygame.Surface((base_size * 2, base_size * 2), pygame.SRCALPHA)
        pygame.draw.circle(withdrawn_sprite, (100, 100, 100), (base_size, base_size), base_size - 3)
        pygame.draw.circle(withdrawn_sprite, (50, 50, 50), (base_size, base_size), base_size - 3, 2)
        # Ojos cerrados
        pygame.draw.line(withdrawn_sprite, (0, 0, 0), (base_size - 8, base_size - 4), (base_size - 4, base_size - 4), 2)
        pygame.draw.line(withdrawn_sprite, (0, 0, 0), (base_size + 4, base_size - 4), (base_size + 8, base_size - 4), 2)
        self.sprites["withdrawing"] = withdrawn_sprite
    
    def render(self, nexus, screen, camera_offset, camera_zoom):
        """Renderiza un Nexus con todos sus efectos visuales.
        
        Args:
            nexus (Nexus): Nexus a renderizar.
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (list): Offset de la cámara [x, y].
            camera_zoom (float): Zoom de la cámara.
        """
        # Calcular posición en pantalla
        screen_x = (nexus.position[0] - camera_offset[0]) * camera_zoom
        screen_y = (nexus.position[1] - camera_offset[1]) * camera_zoom
        
        # Verificar si está en pantalla
        if (screen_x < -50 or screen_x > screen.get_width() + 50 or
            screen_y < -50 or screen_y > screen.get_height() + 50):
            return
        
        # Renderizar territorio si lo tiene
        self._render_territory(nexus, screen, camera_offset, camera_zoom)
        
        # Renderizar indicadores de estrés
        self._render_stress_indicators(nexus, screen, screen_x, screen_y)
        
        # Renderizar sprite principal
        self._render_main_sprite(nexus, screen, screen_x, screen_y, camera_zoom)
        
        # Renderizar indicadores de estado
        self._render_status_indicators(nexus, screen, screen_x, screen_y)
        
        # Renderizar información adicional si está seleccionado
        if hasattr(nexus, 'selected') and nexus.selected:
            self._render_selection_indicators(nexus, screen, screen_x, screen_y)
        
        # Renderizar efectos de partículas
        self._render_particle_effects(nexus, screen, screen_x, screen_y)
    
    def _render_territory(self, nexus, screen, camera_offset, camera_zoom):
        """Renderiza el territorio del Nexus si lo tiene."""
        if not hasattr(nexus, 'territory_center') or not hasattr(nexus, 'territory_size'):
            return
        
        # Calcular posición del territorio en pantalla
        territory_x = (nexus.territory_center[0] - camera_offset[0]) * camera_zoom
        territory_y = (nexus.territory_center[1] - camera_offset[1]) * camera_zoom
        territory_radius = nexus.territory_size * camera_zoom
        
        # Verificar si el territorio está visible
        if (territory_x + territory_radius < 0 or territory_x - territory_radius > screen.get_width() or
            territory_y + territory_radius < 0 or territory_y - territory_radius > screen.get_height()):
            return
        
        # Color del territorio basado en personalidad
        personality_type = getattr(nexus, 'personality_type', 'beta')
        territory_color = self.personality_colors.get(personality_type, (100, 100, 100))
        
        # Renderizar borde del territorio
        if territory_radius > 10:  # Solo si es lo suficientemente grande para ser visible
            pygame.draw.circle(screen, (*territory_color, 50), 
                             (int(territory_x), int(territory_y)), 
                             int(territory_radius), 2)
    
    def _render_stress_indicators(self, nexus, screen, screen_x, screen_y):
        """Renderiza indicadores visuales de estrés."""
        if not hasattr(nexus, 'stress_level'):
            return
        
        stress = nexus.stress_level
        if stress > 0.3:  # Solo mostrar si hay estrés significativo
            # Crear partículas de estrés
            if random.random() < stress * 0.1:
                particle = {
                    "x": screen_x + random.uniform(-15, 15),
                    "y": screen_y - 20 + random.uniform(-5, 5),
                    "vx": random.uniform(-1, 1),
                    "vy": random.uniform(-2, -0.5),
                    "life": 30,
                    "color": (255, int(255 * (1 - stress)), 0)
                }
                self.stress_particles.append(particle)
        
        # Actualizar y renderizar partículas de estrés
        for particle in list(self.stress_particles):
            particle["x"] += particle["vx"]
            particle["y"] += particle["vy"]
            particle["life"] -= 1
            
            if particle["life"] <= 0:
                self.stress_particles.remove(particle)
            else:
                alpha = int(255 * (particle["life"] / 30))
                color = (*particle["color"], alpha)
                
                # Crear superficie temporal para alpha
                temp_surface = pygame.Surface((4, 4), pygame.SRCALPHA)
                pygame.draw.circle(temp_surface, color, (2, 2), 2)
                screen.blit(temp_surface, (particle["x"] - 2, particle["y"] - 2))
    
    def _render_main_sprite(self, nexus, screen, screen_x, screen_y, camera_zoom):
        """Renderiza el sprite principal del Nexus."""
        # Seleccionar sprite basado en estado
        sprite_key = "base"
        if hasattr(nexus, 'state'):
            if nexus.state in ["aggressive", "stressed", "withdrawing"]:
                sprite_key = nexus.state
        
        sprite = self.sprites.get(sprite_key, self.sprites["base"])
        
        # Escalar sprite según zoom
        if camera_zoom != 1.0:
            new_size = (int(sprite.get_width() * camera_zoom), 
                       int(sprite.get_height() * camera_zoom))
            sprite = pygame.transform.scale(sprite, new_size)
        
        # Aplicar tinte basado en personalidad
        if hasattr(nexus, 'personality_type'):
            personality_color = self.personality_colors.get(nexus.personality_type, (255, 255, 255))
            tinted_sprite = sprite.copy()
            tinted_sprite.fill((*personality_color, 100), special_flags=pygame.BLEND_RGBA_MULT)
            sprite = tinted_sprite
        
        # Renderizar sprite
        sprite_rect = sprite.get_rect(center=(int(screen_x), int(screen_y)))
        screen.blit(sprite, sprite_rect)
        
        # Renderizar dirección
        if hasattr(nexus, 'direction'):
            direction_length = 15 * camera_zoom
            end_x = screen_x + math.cos(nexus.direction) * direction_length
            end_y = screen_y + math.sin(nexus.direction) * direction_length
            pygame.draw.line(screen, (255, 255, 255), 
                           (int(screen_x), int(screen_y)), 
                           (int(end_x), int(end_y)), 2)
    
    def _render_status_indicators(self, nexus, screen, screen_x, screen_y):
        """Renderiza indicadores de estado (salud, energía, etc.)."""
        # Barra de salud
        if nexus.health < 100:
            health_width = 30
            health_height = 4
            health_x = screen_x - health_width // 2
            health_y = screen_y - 35
            
            # Fondo de la barra
            pygame.draw.rect(screen, (100, 100, 100), 
                           (health_x, health_y, health_width, health_height))
            
            # Barra de salud
            health_ratio = nexus.health / 100
            health_color = (255 * (1 - health_ratio), 255 * health_ratio, 0)
            pygame.draw.rect(screen, health_color, 
                           (health_x, health_y, health_width * health_ratio, health_height))
        
        # Barra de energía
        if nexus.energy < 100:
            energy_width = 30
            energy_height = 4
            energy_x = screen_x - energy_width // 2
            energy_y = screen_y - 30
            
            # Fondo de la barra
            pygame.draw.rect(screen, (100, 100, 100), 
                           (energy_x, energy_y, energy_width, energy_height))
            
            # Barra de energía
            energy_ratio = nexus.energy / 100
            energy_color = (255, 255 * energy_ratio, 0)
            pygame.draw.rect(screen, energy_color, 
                           (energy_x, energy_y, energy_width * energy_ratio, energy_height))
    
    def _render_selection_indicators(self, nexus, screen, screen_x, screen_y):
        """Renderiza indicadores cuando el Nexus está seleccionado."""
        # Círculo de selección
        pygame.draw.circle(screen, (255, 255, 0), 
                         (int(screen_x), int(screen_y)), 35, 3)
        
        # Nombre del Nexus
        font = pygame.font.Font(None, 24)
        name_surface = font.render(nexus.name, True, (255, 255, 255))
        name_rect = name_surface.get_rect(center=(screen_x, screen_y - 50))
        
        # Fondo semi-transparente para el nombre
        bg_surface = pygame.Surface((name_rect.width + 10, name_rect.height + 4), pygame.SRCALPHA)
        bg_surface.fill((0, 0, 0, 150))
        screen.blit(bg_surface, (name_rect.x - 5, name_rect.y - 2))
        screen.blit(name_surface, name_rect)
    
    def _render_particle_effects(self, nexus, screen, screen_x, screen_y):
        """Renderiza efectos de partículas específicos del estado."""
        if not hasattr(nexus, 'state'):
            return
        
        # Efectos específicos por estado
        if nexus.state == "socializing":
            # Corazones pequeños
            if random.random() < 0.05:
                heart_x = screen_x + random.uniform(-10, 10)
                heart_y = screen_y - 20 + random.uniform(-5, 5)
                pygame.draw.circle(screen, (255, 100, 150), (int(heart_x), int(heart_y)), 2)
        
        elif nexus.state == "learning":
            # Símbolos de conocimiento
            if random.random() < 0.03:
                symbol_x = screen_x + random.uniform(-15, 15)
                symbol_y = screen_y - 25 + random.uniform(-5, 5)
                pygame.draw.circle(screen, (100, 255, 100), (int(symbol_x), int(symbol_y)), 1)
        
        elif nexus.state == "building":
            # Partículas de construcción
            if random.random() < 0.08:
                dust_x = screen_x + random.uniform(-20, 20)
                dust_y = screen_y + random.uniform(-10, 10)
                pygame.draw.rect(screen, (139, 69, 19), (int(dust_x), int(dust_y), 2, 2))
    
    def update_animations(self):
        """Actualiza las animaciones de todos los Nexus."""
        # Actualizar estados de animación
        for nexus_id, state in self.animation_states.items():
            for anim_name, anim_data in state.items():
                anim_data["frame"] = (anim_data["frame"] + 1) % anim_data["total_frames"]
    
    def create_emotion_bubble(self, nexus, emotion_type):
        """Crea una burbuja de emoción sobre un Nexus.
        
        Args:
            nexus (Nexus): Nexus que muestra la emoción.
            emotion_type (str): Tipo de emoción ('happy', 'sad', 'angry', 'fear').
        """
        bubble = {
            "nexus": nexus,
            "type": emotion_type,
            "life": 60,  # Duración en frames
            "y_offset": -40
        }
        self.emotion_bubbles.append(bubble)
    
    def render_emotion_bubbles(self, screen, camera_offset, camera_zoom):
        """Renderiza las burbujas de emoción."""
        for bubble in list(self.emotion_bubbles):
            nexus = bubble["nexus"]
            
            # Calcular posición
            screen_x = (nexus.position[0] - camera_offset[0]) * camera_zoom
            screen_y = (nexus.position[1] - camera_offset[1]) * camera_zoom + bubble["y_offset"]
            
            # Colores por tipo de emoción
            emotion_colors = {
                "happy": (255, 255, 0),
                "sad": (100, 100, 255),
                "angry": (255, 100, 100),
                "fear": (255, 150, 0)
            }
            
            color = emotion_colors.get(bubble["type"], (255, 255, 255))
            
            # Renderizar burbuja
            pygame.draw.circle(screen, color, (int(screen_x), int(screen_y)), 8)
            pygame.draw.circle(screen, (255, 255, 255), (int(screen_x), int(screen_y)), 8, 2)
            
            # Actualizar burbuja
            bubble["life"] -= 1
            bubble["y_offset"] -= 0.5
            
            if bubble["life"] <= 0:
                self.emotion_bubbles.remove(bubble)
