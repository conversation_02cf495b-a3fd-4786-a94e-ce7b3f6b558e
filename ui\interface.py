#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo de interfaz de usuario para el simulador Nexus.

Implementa la interfaz gráfica que permite al usuario interactuar con el simulador.
"""

import pygame
import random
import math
from pygame.locals import *

class Interface:
    """Clase que gestiona la interfaz de usuario del simulador."""

    def __init__(self, screen, config):
        """Inicializa la interfaz de usuario.

        Args:
            screen (pygame.Surface): Superficie principal de renderizado.
            config (Config): Configuración del simulador.
        """
        self.screen = screen
        self.config = config
        self.font = config.FONT
        self.selected_nexus = None
        self.show_info_panel = True
        self.show_debug = False
        self.show_help = False
        self.teaching_mode = False
        self.show_save_dialog = False
        self.show_load_dialog = False
        self.show_exit_dialog = False  # Diálogo de confirmación para salir
        self.save_filename = ""
        self.save_files = []
        self.selected_save_file = 0
        self.info_panel_collapsed = False  # Estado del panel de información
        self.available_teachings = [
            "Hacer fuego",
            "Construir refugio",
            "Fabricar lanza",
            "Recolectar bayas",
            "Pescar",
            "Cazar en grupo"
        ]
        self.teaching_selection = 0

        # Configuración del menú desplegable
        self.menu_collapsed = True
        self.menu_button = Button(10, 10, 40, 40, "≡", self.config)
        self.menu_animation = 0  # 0 = cerrado, 1 = abierto (para animación)

        # Categorías del menú
        self.menu_categories = {
            "nexus": {"name": "Nexus", "expanded": False},
            "simulation": {"name": "Simulación", "expanded": False},
            "camera": {"name": "Cámara", "expanded": False},
            "system": {"name": "Sistema", "expanded": False}
        }

        # Botón para colapsar el panel de información
        self.info_collapse_button = Button(0, 0, 30, 30, "X", self.config)

        # Botones de la interfaz (organizados por categorías)
        self.buttons = {
            # Categoría Nexus
            "select_nexus": Button(60, 60, 150, 30, "Seleccionar Nexus", self.config, category="nexus"),
            "follow": Button(60, 95, 150, 30, "Seguir Nexus", self.config, category="nexus"),
            "teach": Button(60, 130, 150, 30, "Enseñar", self.config, category="nexus"),

            # Categoría Simulación
            "toggle_realtime": Button(60, 60, 150, 30, "Tiempo Real", self.config, category="simulation"),
            "toggle_pause": Button(60, 95, 150, 30, "Pausar/Reanudar", self.config, category="simulation"),
            "speed_up": Button(60, 130, 150, 30, "Acelerar Tiempo", self.config, category="simulation"),
            "speed_down": Button(60, 165, 150, 30, "Ralentizar Tiempo", self.config, category="simulation"),

            # Categoría Cámara
            "reset_camera": Button(60, 60, 150, 30, "Resetear Cámara", self.config, category="camera"),
            "zoom_in": Button(60, 95, 150, 30, "Acercar", self.config, category="camera"),
            "zoom_out": Button(60, 130, 150, 30, "Alejar", self.config, category="camera"),
            "zoom_max": Button(60, 165, 150, 30, "Ver Todo", self.config, category="camera"),

            # Categoría Sistema
            "toggle_info": Button(60, 60, 150, 30, "Mostrar/Ocultar Info", self.config, category="system"),
            "toggle_debug": Button(60, 95, 150, 30, "Modo Debug", self.config, category="system"),
            "help": Button(60, 130, 150, 30, "Ayuda", self.config, category="system"),
            "save_game": Button(60, 165, 150, 30, "Guardar Partida", self.config, category="system"),
            "load_game": Button(60, 200, 150, 30, "Cargar Partida", self.config, category="system")
        }

    def handle_event(self, event, world):
        """Maneja los eventos de entrada del usuario.

        Args:
            event (pygame.event.Event): Evento a manejar.
            world (World): Referencia al mundo de la simulación.

        Returns:
            bool: True si el evento fue manejado, False en caso contrario.
        """
        # Manejar diálogo de salida
        if self.show_exit_dialog:
            if event.type == KEYDOWN:
                if event.key == K_ESCAPE:
                    # Cancelar salida
                    self.show_exit_dialog = False
                    return True
                elif event.key == K_RETURN:
                    # Confirmar salida
                    pygame.event.post(pygame.event.Event(QUIT))
                    return True
            elif event.type == MOUSEBUTTONDOWN and event.button == 1:
                mouse_pos = pygame.mouse.get_pos()
                # Verificar clic en botones de confirmación
                dialog_width = 400
                dialog_height = 200
                dialog_x = (self.screen.get_width() - dialog_width) // 2
                dialog_y = (self.screen.get_height() - dialog_height) // 2

                # Botón Sí
                yes_rect = pygame.Rect(dialog_x + 80, dialog_y + 120, 100, 40)
                if yes_rect.collidepoint(mouse_pos):
                    # Guardar partida automáticamente antes de salir
                    from utils.save_load import save_game
                    save_game(world, "autosave.dat")
                    pygame.event.post(pygame.event.Event(QUIT))
                    return True

                # Botón No
                no_rect = pygame.Rect(dialog_x + 220, dialog_y + 120, 100, 40)
                if no_rect.collidepoint(mouse_pos):
                    self.show_exit_dialog = False
                    return True

            return True  # Consumir todos los eventos mientras el diálogo está abierto

        # Manejar diálogo de guardado
        if self.show_save_dialog:
            if event.type == KEYDOWN:
                if event.key == K_ESCAPE:
                    self.show_save_dialog = False
                    return True
                elif event.key == K_RETURN:
                    # Guardar partida
                    from utils.save_load import save_game
                    filename = f"{self.save_filename}.dat"
                    save_game(world, filename)
                    self.show_save_dialog = False
                    return True
                elif event.key == K_BACKSPACE:
                    self.save_filename = self.save_filename[:-1]
                    return True
                elif event.unicode and event.unicode.isprintable():
                    self.save_filename += event.unicode
                    return True
            return True  # Consumir todos los eventos mientras el diálogo está abierto

        # Manejar diálogo de carga
        if self.show_load_dialog:
            if event.type == KEYDOWN:
                if event.key == K_ESCAPE:
                    self.show_load_dialog = False
                    return True
                elif event.key == K_RETURN:
                    # Cargar partida
                    if self.save_files:
                        from utils.save_load import load_game
                        filepath = self.save_files[self.selected_save_file]["filepath"]
                        new_world = load_game(filepath, world.config)
                        # Actualizar el mundo actual con el cargado
                        world.entities = new_world.entities
                        world.resources = new_world.resources
                        world.predators = new_world.predators
                        world.time = new_world.time
                        world.weather = new_world.weather
                        world.weather_intensity = new_world.weather_intensity
                        world.weather_duration = new_world.weather_duration
                        world.season = new_world.season
                        world.season_day = new_world.season_day
                        world.entity_id_counter = new_world.entity_id_counter
                        world.discovered_technologies = new_world.discovered_technologies
                        # Resetear selección
                        self.selected_nexus = None
                    self.show_load_dialog = False
                    return True
                elif event.key == K_UP:
                    self.selected_save_file = (self.selected_save_file - 1) % len(self.save_files)
                    return True
                elif event.key == K_DOWN:
                    self.selected_save_file = (self.selected_save_file + 1) % len(self.save_files)
                    return True
            return True  # Consumir todos los eventos mientras el diálogo está abierto

        # Manejar clics en botones
        if event.type == MOUSEBUTTONDOWN and event.button == 1:  # Clic izquierdo
            mouse_pos = pygame.mouse.get_pos()

            # Verificar clic en botón de colapsar panel de información
            if self.info_collapse_button.is_clicked(mouse_pos):
                self.info_panel_collapsed = not self.info_panel_collapsed
                return True

            # Verificar clic en botón de menú
            if self.menu_button.is_clicked(mouse_pos):
                self.menu_collapsed = not self.menu_collapsed
                return True

            # Verificar clics en categorías del menú
            if not self.menu_collapsed:
                category_clicked = self.handle_menu_category_click(mouse_pos)
                if category_clicked:
                    return True

            # Verificar clics en botones visibles
            for button_name, button in self.buttons.items():
                if button.visible and button.is_clicked(mouse_pos):
                    self.handle_button_click(button_name, world)
                    return True

            # Verificar clic en Nexus
            if not self.teaching_mode:
                self.handle_nexus_selection(mouse_pos, world)

            # Iniciar arrastre de cámara
            if not self.teaching_mode and not self.show_help:
                world.start_camera_drag(mouse_pos)

        # Manejar arrastre de cámara
        elif event.type == MOUSEMOTION and world.camera_drag:
            world.update_camera_drag(pygame.mouse.get_pos())

        # Finalizar arrastre de cámara
        elif event.type == MOUSEBUTTONUP and event.button == 1 and world.camera_drag:
            world.stop_camera_drag()

        # Manejar zoom con rueda del ratón
        elif event.type == MOUSEWHEEL:
            # Zoom in/out según dirección de la rueda
            world.handle_camera_zoom(event.y > 0)

        # Manejar teclas en modo enseñanza
        elif event.type == KEYDOWN and self.teaching_mode:
            if event.key == K_UP:
                self.teaching_selection = (self.teaching_selection - 1) % len(self.available_teachings)
            elif event.key == K_DOWN:
                self.teaching_selection = (self.teaching_selection + 1) % len(self.available_teachings)
            elif event.key == K_RETURN:
                self.teach_selected_knowledge(world)
                self.teaching_mode = False
            elif event.key == K_ESCAPE:
                self.teaching_mode = False

        # Controles de tiempo real
        elif event.type == KEYDOWN:
            if event.key == K_ESCAPE:
                # Mostrar diálogo de confirmación para salir
                self.show_exit_dialog = True
                return True
            elif event.key == K_SPACE:
                # Pausar/reanudar simulación
                world.toggle_pause()
                return True
            elif event.key == K_t:
                # Activar/desactivar tiempo real
                world.toggle_real_time()
                return True
            elif event.key == K_PLUS or event.key == K_KP_PLUS:
                # Aumentar velocidad de tiempo
                world.set_real_time_factor(world.real_time_factor + 0.5)
                return True
            elif event.key == K_MINUS or event.key == K_KP_MINUS:
                # Reducir velocidad de tiempo
                world.set_real_time_factor(world.real_time_factor - 0.5)
                return True
            elif event.key == K_m:
                # Mostrar/ocultar menú
                self.menu_collapsed = not self.menu_collapsed
                return True
            elif event.key == K_i:
                # Mostrar/ocultar panel de información
                self.info_panel_collapsed = not self.info_panel_collapsed
                return True

        return False

    def handle_menu_category_click(self, mouse_pos):
        """Maneja los clics en las categorías del menú.

        Args:
            mouse_pos (tuple): Posición del ratón (x, y).

        Returns:
            bool: True si se hizo clic en una categoría, False en caso contrario.
        """
        menu_x = 10
        menu_y = 60
        category_y = menu_y + 10

        for category_id, category_info in self.menu_categories.items():
            # Verificar si el clic está en esta categoría
            category_rect = pygame.Rect(menu_x + 10, category_y, 200, 30)

            if category_rect.collidepoint(mouse_pos):
                # Alternar estado de expansión
                self.menu_categories[category_id]["expanded"] = not self.menu_categories[category_id]["expanded"]
                return True

            # Actualizar posición para la siguiente categoría
            if category_info["expanded"]:
                # Contar botones en esta categoría
                buttons_in_category = sum(1 for button in self.buttons.values() if button.category == category_id)
                category_y += 35 + buttons_in_category * 35
            else:
                category_y += 35

        return False

    def handle_button_click(self, button_name, world):
        """Maneja los clics en botones de la interfaz.

        Args:
            button_name (str): Nombre del botón clicado.
            world (World): Referencia al mundo de la simulación.
        """
        if button_name == "select_nexus":
            # Seleccionar Nexus aleatorio vivo
            alive_nexus = [n for n in world.entities if n.alive]
            if alive_nexus:
                self.selected_nexus = random.choice(alive_nexus)

        elif button_name == "follow":
            # Seguir al Nexus seleccionado con la cámara
            if self.selected_nexus and self.selected_nexus.alive:
                world.set_camera_target(self.selected_nexus)

        elif button_name == "teach":
            # Activar modo enseñanza
            if self.selected_nexus and self.selected_nexus.alive:
                self.teaching_mode = True
                self.teaching_selection = 0

        elif button_name == "toggle_info":
            # Mostrar/ocultar panel de información
            self.show_info_panel = not self.show_info_panel

        elif button_name == "toggle_debug":
            # Activar/desactivar modo debug
            self.show_debug = not self.show_debug

        elif button_name == "help":
            # Mostrar/ocultar ayuda
            self.show_help = not self.show_help

        # Nuevos botones para control de tiempo y cámara
        elif button_name == "toggle_realtime":
            # Activar/desactivar tiempo real
            world.toggle_real_time()

        elif button_name == "toggle_pause":
            # Pausar/reanudar simulación
            world.toggle_pause()

        elif button_name == "speed_up":
            # Aumentar velocidad del tiempo
            world.set_real_time_factor(world.real_time_factor + 0.5)

        elif button_name == "speed_down":
            # Reducir velocidad del tiempo
            world.set_real_time_factor(world.real_time_factor - 0.5)

        elif button_name == "reset_camera":
            # Resetear zoom y posición de la cámara
            world.camera_zoom = 1.0
            world.camera_offset = [0, 0]
            world.camera_target = None

        elif button_name == "zoom_in":
            # Acercar la cámara
            world.handle_camera_zoom(True)

        elif button_name == "zoom_out":
            # Alejar la cámara
            world.handle_camera_zoom(False)

        elif button_name == "zoom_max":
            # Ver todo el mapa
            world.camera_zoom = 0.1  # Zoom muy lejano
            world.camera_offset = [world.world_radius - (self.screen.get_width() / world.camera_zoom) / 2,
                                 world.world_radius - (self.screen.get_height() / world.camera_zoom) / 2]
            world.camera_target = None

        # Botones para guardar y cargar partidas
        elif button_name == "save_game":
            # Mostrar diálogo de guardado
            self.show_save_dialog = True
            self.save_filename = f"nexus_save_{world.time}"

        elif button_name == "load_game":
            # Mostrar diálogo de carga
            from utils.save_load import get_save_files
            self.save_files = get_save_files()
            if self.save_files:
                self.show_load_dialog = True
                self.selected_save_file = 0

    def handle_nexus_selection(self, mouse_pos, world):
        """Maneja la selección de un Nexus mediante clic.

        Args:
            mouse_pos (tuple): Posición del ratón (x, y).
            world (World): Referencia al mundo de la simulación.
        """
        # Punto central de la pantalla
        center_x = self.screen.get_width() // 2
        center_y = self.screen.get_height() // 2

        # Convertir posición de pantalla a posición de mundo isométrico
        # Primero ajustamos por el offset de la cámara
        screen_x = mouse_pos[0] + world.camera_offset[0] * world.camera_zoom
        screen_y = mouse_pos[1] + world.camera_offset[1] * world.camera_zoom

        # Luego convertimos de coordenadas de pantalla a coordenadas isométricas
        # Estas fórmulas son la inversa de la transformación isométrica
        iso_x = (screen_x - center_x) / world.camera_zoom
        iso_y = (screen_y - center_y) / world.camera_zoom

        # Convertir de isométrico a coordenadas de mundo
        world_x = iso_x * 2 + iso_y * 4
        world_y = -iso_x * 2 + iso_y * 4

        # Buscar Nexus cercano al clic con un margen de selección más amplio
        closest_nexus = None
        closest_distance = float('inf')

        for nexus in world.entities:
            if not nexus.alive:
                continue

            # Calcular distancia en coordenadas de mundo
            distance = math.sqrt((nexus.position[0] - world_x)**2 +
                               (nexus.position[1] - world_y)**2)

            # Usar un margen de selección más amplio
            selection_margin = nexus.size * 3

            if distance < selection_margin and distance < closest_distance:
                closest_nexus = nexus
                closest_distance = distance

        # Seleccionar el Nexus más cercano si se encontró alguno
        if closest_nexus:
            self.selected_nexus = closest_nexus
            # Marcar el Nexus como seleccionado para visualización
            for entity in world.entities:
                entity.selected = (entity == closest_nexus)
            return

    def teach_selected_knowledge(self, world):
        """Enseña el conocimiento seleccionado al Nexus seleccionado.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        if not self.selected_nexus or not self.selected_nexus.alive:
            return

        knowledge = self.available_teachings[self.teaching_selection]

        # Verificar si ya tiene ese conocimiento
        if knowledge in self.selected_nexus.knowledge:
            return

        # Enseñar conocimiento
        self.selected_nexus.learn(knowledge)

        # Añadir recuerdo del aprendizaje
        self.selected_nexus.add_memory(f"Aprendió {knowledge} del jugador")

        # Aumentar felicidad por el nuevo conocimiento
        self.selected_nexus.emotions["happiness"] = min(1.0, self.selected_nexus.emotions["happiness"] + 0.2)

    def render(self, world):
        """Renderiza la interfaz de usuario.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Actualizar visibilidad de botones según menú
        self.update_menu_buttons()

        # Renderizar botón de menú principal
        self.menu_button.render(self.screen)

        # Renderizar menú desplegable si está abierto
        if not self.menu_collapsed:
            self.render_menu()

        # Renderizar botones visibles
        for button in self.buttons.values():
            if button.visible:
                button.render(self.screen)

        # Renderizar información del mundo (minimalista)
        self.render_world_info(world)

        # Renderizar información del Nexus seleccionado
        if self.selected_nexus and self.show_info_panel:
            self.render_nexus_info(self.selected_nexus, world)

        # Renderizar modo enseñanza
        if self.teaching_mode:
            self.render_teaching_mode()

        # Renderizar ayuda
        if self.show_help:
            self.render_help()

        # Renderizar información de depuración
        if self.show_debug:
            self.render_debug_info(world)

        # Renderizar diálogo de guardado
        if self.show_save_dialog:
            self.render_save_dialog()

        # Renderizar diálogo de carga
        if self.show_load_dialog:
            self.render_load_dialog()

        # Renderizar diálogo de confirmación de salida
        if self.show_exit_dialog:
            self.render_exit_dialog()

        # Renderizar métricas Universe 25 si están disponibles
        if hasattr(world, "stress_psychology_system"):
            self.render_universe25_metrics(world)

    def update_menu_buttons(self):
        """Actualiza la visibilidad y posición de los botones según el estado del menú."""
        # Si el menú está colapsado, ocultar todos los botones
        if self.menu_collapsed:
            for button in self.buttons.values():
                button.visible = False
            return

        # Calcular posiciones para las categorías
        category_y = 60
        category_spacing = 35

        # Actualizar visibilidad y posición de botones por categoría
        for category_id, category_info in self.menu_categories.items():
            # Procesar esta categoría

            # Mostrar botones de esta categoría si está expandida
            if category_info["expanded"]:
                button_y = category_y + 35  # Espacio para el botón de categoría

                # Mostrar botones de esta categoría
                for button_name, button in self.buttons.items():
                    if button.category == category_id:
                        button.visible = True
                        button.set_position(60, button_y)
                        button_y += 35  # Espacio entre botones

                # Actualizar posición para la siguiente categoría
                category_y = button_y + 10
            else:
                # Ocultar botones de esta categoría
                for button_name, button in self.buttons.items():
                    if button.category == category_id:
                        button.visible = False

                # Actualizar posición para la siguiente categoría
                category_y += category_spacing

    def render_menu(self):
        """Renderiza el menú desplegable."""
        # Fondo del menú
        menu_width = 220
        menu_height = 400

        menu_surface = pygame.Surface((menu_width, menu_height), pygame.SRCALPHA)
        menu_surface.fill((30, 30, 40, 200))  # Fondo semi-transparente

        # Borde del menú
        pygame.draw.rect(menu_surface, (100, 100, 120), (0, 0, menu_width, menu_height), 2)

        # Renderizar categorías
        category_y = 10
        for category_id, category_info in self.menu_categories.items():
            # Fondo de categoría
            category_bg_color = (60, 60, 80, 200) if category_info["expanded"] else (40, 40, 60, 200)
            pygame.draw.rect(menu_surface, category_bg_color, (10, category_y, menu_width - 20, 30))

            # Texto de categoría
            category_text = self.font.render(category_info["name"], True, (220, 220, 220))
            menu_surface.blit(category_text, (20, category_y + 5))

            # Indicador de expansión
            indicator = "▼" if category_info["expanded"] else "►"
            indicator_text = self.font.render(indicator, True, (220, 220, 220))
            menu_surface.blit(indicator_text, (menu_width - 30, category_y + 5))

            # Actualizar posición para la siguiente categoría
            if category_info["expanded"]:
                # Contar botones en esta categoría
                buttons_in_category = sum(1 for button in self.buttons.values() if button.category == category_id)
                category_y += 35 + buttons_in_category * 35  # Altura de categoría + altura de botones
            else:
                category_y += 35  # Solo altura de categoría

        # Renderizar en pantalla
        self.screen.blit(menu_surface, (10, 60))

    def render_world_info(self, world):
        """Renderiza información general del mundo.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Información básica del mundo
        info_text = [
            f"Tiempo: {world.time}",
            f"Clima: {world.weather}"
        ]

        # Información de tiempo real
        if world.real_time_enabled:
            estado = "PAUSADO" if world.real_time_paused else "ACTIVO"
            info_text.append(f"Tiempo real: {estado}")
            info_text.append(f"Velocidad: x{world.real_time_factor:.1f}")
        else:
            info_text.append("Tiempo real: DESACTIVADO")

        # Información de entidades
        info_text.extend([
            f"Nexus vivos: {sum(1 for n in world.entities if n.alive)}/{len(world.entities)}",
            f"Recursos: {len(world.resources)}",
            f"Depredadores: {len(world.predators)}",
            f"Tribus: {len(getattr(world, 'tribes', []))}"
        ])

        # Renderizar en esquina superior derecha
        x = self.screen.get_width() - 200
        y = 50

        # Fondo semi-transparente
        info_surface = pygame.Surface((190, len(info_text) * 20 + 10), pygame.SRCALPHA)
        info_surface.fill(self.config.UI_BACKGROUND_COLOR)
        self.screen.blit(info_surface, (x, y))

        # Texto
        for i, text in enumerate(info_text):
            text_surface = self.font.render(text, True, self.config.UI_TEXT_COLOR)
            self.screen.blit(text_surface, (x + 10, y + 10 + i * 20))

    def render_nexus_info(self, nexus, world):
        """Renderiza información detallada del Nexus seleccionado.

        Args:
            nexus (Nexus): Nexus seleccionado.
            world (World): Referencia al mundo de la simulación.
        """
        # Si el panel está colapsado, solo mostrar un pequeño indicador
        if self.info_panel_collapsed:
            # Pequeño botón para expandir el panel
            expand_button = pygame.Rect(10, 10, 30, 30)
            pygame.draw.rect(self.screen, (50, 50, 70, 200), expand_button)
            pygame.draw.rect(self.screen, (200, 200, 220), expand_button, 2)

            # Símbolo de expansión (+)
            pygame.draw.line(self.screen, (255, 255, 255),
                            (expand_button.centerx - 8, expand_button.centery),
                            (expand_button.centerx + 8, expand_button.centery), 2)
            pygame.draw.line(self.screen, (255, 255, 255),
                            (expand_button.centerx, expand_button.centery - 8),
                            (expand_button.centerx, expand_button.centery + 8), 2)

            # Actualizar posición del botón para detectar clics
            self.info_collapse_button.set_position(expand_button.x, expand_button.y)
            self.info_collapse_button.rect.width = expand_button.width
            self.info_collapse_button.rect.height = expand_button.height

            return

        # Verificar si el Nexus sigue vivo
        if not nexus.alive:
            info_text = [f"Nombre: {nexus.name} (FALLECIDO)", f"Edad: {nexus.age}", ""]
        else:
            # Información básica
            info_text = [
                f"Nombre: {nexus.name}",
                f"Edad: {nexus.age}",
                f"Energía: {int(nexus.energy)}/100",
                f"Salud: {int(nexus.health)}/100",
                f"Estado: {nexus.state}",
                "",
                "Atributos:"
            ]

            # Atributos
            for attr, value in nexus.attributes.items():
                info_text.append(f"  {attr.capitalize()}: {int(value * 100)}%")

            info_text.append("")

            # Emociones
            info_text.append("Emociones:")
            for emotion, value in nexus.emotions.items():
                info_text.append(f"  {emotion.capitalize()}: {int(value * 100)}%")

            info_text.append("")

            # Conocimientos
            info_text.append("Conocimientos:")
            if nexus.knowledge:
                for knowledge in sorted(nexus.knowledge):
                    info_text.append(f"  - {knowledge}")
            else:
                info_text.append("  (Ninguno)")

            info_text.append("")

            # Tribu
            if nexus.tribe:
                info_text.append(f"Tribu: {nexus.tribe.name}")
                info_text.append(f"Miembros: {len(nexus.tribe.members)}")
                info_text.append(f"Cohesión: {int(nexus.tribe.cohesion * 100)}%")
                if nexus == nexus.tribe.leader:
                    info_text.append("Rol: Líder")
                else:
                    info_text.append("Rol: Miembro")
            else:
                info_text.append("Tribu: Ninguna")

            info_text.append("")

            # Recuerdos recientes
            info_text.append("Recuerdos recientes:")
            recent_memories = sorted(nexus.memory, key=lambda m: m["time"], reverse=True)[:3]
            if recent_memories:
                for memory in recent_memories:
                    info_text.append(f"  - {memory['text']}")
            else:
                info_text.append("  (Ninguno)")

        # Renderizar en panel lateral
        panel_width = 300
        panel_height = min(self.screen.get_height() - 100, len(info_text) * 20 + 20)
        x = 10
        y = 50

        # Fondo semi-transparente
        info_surface = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        info_surface.fill(self.config.UI_BACKGROUND_COLOR)
        pygame.draw.rect(info_surface, self.config.UI_BORDER_COLOR,
                        (0, 0, panel_width, panel_height), self.config.UI_BORDER_WIDTH)
        self.screen.blit(info_surface, (x, y))

        # Botón para colapsar el panel
        collapse_button = pygame.Rect(x + panel_width - 30, y + 5, 25, 25)
        pygame.draw.rect(self.screen, (70, 70, 90), collapse_button)
        pygame.draw.rect(self.screen, (200, 200, 220), collapse_button, 2)

        # Símbolo X
        pygame.draw.line(self.screen, (255, 255, 255),
                        (collapse_button.left + 5, collapse_button.top + 5),
                        (collapse_button.right - 5, collapse_button.bottom - 5), 2)
        pygame.draw.line(self.screen, (255, 255, 255),
                        (collapse_button.right - 5, collapse_button.top + 5),
                        (collapse_button.left + 5, collapse_button.bottom - 5), 2)

        # Actualizar posición del botón para detectar clics
        self.info_collapse_button.set_position(collapse_button.x, collapse_button.y)
        self.info_collapse_button.rect.width = collapse_button.width
        self.info_collapse_button.rect.height = collapse_button.height

        # Texto
        for i, text in enumerate(info_text):
            if i * 20 + 20 > panel_height - 10:
                break  # Evitar renderizar fuera del panel
            text_surface = self.font.render(text, True, self.config.UI_TEXT_COLOR)
            self.screen.blit(text_surface, (x + 10, y + 10 + i * 20))

        # Indicador visual del Nexus seleccionado
        if nexus.alive:
            screen_x = int(nexus.position[0] - world.camera_offset[0])
            screen_y = int(nexus.position[1] - world.camera_offset[1])
            pygame.draw.circle(self.screen, (255, 255, 255), (screen_x, screen_y), nexus.size + 5, 2)

    def render_teaching_mode(self):
        """Renderiza la interfaz de modo enseñanza."""
        # Fondo semi-transparente
        panel_width = 300
        panel_height = len(self.available_teachings) * 30 + 60
        x = (self.screen.get_width() - panel_width) // 2
        y = (self.screen.get_height() - panel_height) // 2

        panel = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        panel.fill((30, 30, 30, 230))  # Fondo oscuro semi-transparente
        pygame.draw.rect(panel, (255, 255, 255), (0, 0, panel_width, panel_height), 2)

        # Título
        title = self.font.render("Selecciona conocimiento para enseñar", True, (255, 255, 255))
        panel.blit(title, ((panel_width - title.get_width()) // 2, 10))

        # Instrucciones
        instructions = self.font.render("(Usa ↑↓ para seleccionar, Enter para enseñar)", True, (200, 200, 200))
        panel.blit(instructions, ((panel_width - instructions.get_width()) // 2, 30))

        # Lista de conocimientos
        for i, knowledge in enumerate(self.available_teachings):
            # Resaltar seleccionado
            if i == self.teaching_selection:
                pygame.draw.rect(panel, (100, 100, 255), (10, 60 + i * 30, panel_width - 20, 25))

            # Verificar si el Nexus ya tiene este conocimiento
            has_knowledge = self.selected_nexus and knowledge in self.selected_nexus.knowledge
            color = (150, 150, 150) if has_knowledge else (255, 255, 255)

            text = self.font.render(knowledge, True, color)
            panel.blit(text, (20, 60 + i * 30))

            # Indicador de conocimiento ya adquirido
            if has_knowledge:
                indicator = self.font.render("(Ya conocido)", True, (150, 150, 150))
                panel.blit(indicator, (panel_width - indicator.get_width() - 20, 60 + i * 30))

        self.screen.blit(panel, (x, y))

    def render_help(self):
        """Renderiza la pantalla de ayuda."""
        # Fondo semi-transparente
        panel_width = 600
        panel_height = 500
        x = (self.screen.get_width() - panel_width) // 2
        y = (self.screen.get_height() - panel_height) // 2

        panel = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        panel.fill((30, 30, 30, 230))  # Fondo oscuro semi-transparente
        pygame.draw.rect(panel, (255, 255, 255), (0, 0, panel_width, panel_height), 2)

        # Título
        title = self.font.render("Ayuda - Simulador Nexus", True, (255, 255, 255))
        panel.blit(title, ((panel_width - title.get_width()) // 2, 10))

        # Contenido de ayuda
        help_text = [
            "Controles:",
            "- Seleccionar Nexus: Haz clic en un Nexus o usa el botón 'Seleccionar Nexus'",
            "- Seguir Nexus: La cámara seguirá al Nexus seleccionado",
            "- Enseñar: Enseña conocimientos al Nexus seleccionado",
            "- Espacio: Pausa/reanuda la simulación",
            "- ESC: Salir",
            "",
            "Controles de cámara:",
            "- Arrastrar: Mantén presionado el botón izquierdo del ratón y mueve",
            "- Zoom: Usa la rueda del ratón para acercar/alejar",
            "",
            "Controles de tiempo:",
            "- T: Activar/desactivar tiempo real",
            "- Espacio: Pausar/reanudar simulación",
            "- +/-: Aumentar/reducir velocidad del tiempo",
            "",
            "Estados de los Nexus:",
            "- Idle: En reposo",
            "- Seeking Food: Buscando comida",
            "- Seeking Mate: Buscando pareja",
            "- Socializing: Socializando con otros Nexus",
            "- Exploring: Explorando el entorno",
            "- Resting: Descansando para recuperar energía",
            "- Fleeing: Huyendo de un depredador",
            "- Attacking: Atacando a otro Nexus",
            "",
            "Colores de recursos:",
            "- Amarillo: Comida",
            "- Azul: Agua",
            "- Marrón: Madera",
            "- Gris: Piedra",
            "",
            "Objetivo:",
            "Observa cómo evoluciona la civilización desde un solo individuo.",
            "Enseña habilidades clave y observa cómo se transmiten entre generaciones."
        ]

        for i, text in enumerate(help_text):
            text_surface = self.font.render(text, True, (255, 255, 255))
            panel.blit(text_surface, (20, 40 + i * 20))

        self.screen.blit(panel, (x, y))

    def render_debug_info(self, world):
        """Renderiza información de depuración.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Estadísticas detalladas
        debug_text = [
            f"FPS: {int(pygame.time.Clock().get_fps())}",
            f"Entidades: {len(world.entities)}",
            f"Recursos: {len(world.resources)}",
            f"Depredadores: {len(world.predators)}",
            f"Tribus: {len(world.tribes)}",
            f"Cámara: ({int(world.camera_offset[0])}, {int(world.camera_offset[1])})",
            "",
            "Estadísticas de población:",
            f"Edad media: {self.get_average_age(world):.1f}",
            f"Energía media: {self.get_average_energy(world):.1f}",
            f"Conocimientos totales: {len(self.get_all_knowledge(world))}",
            "",
            "Distribución de estados:"
        ]

        # Añadir distribución de estados
        state_counts = self.get_state_distribution(world)
        for state, count in state_counts.items():
            debug_text.append(f"  {state}: {count}")

        # Renderizar en esquina inferior derecha
        x = self.screen.get_width() - 250
        y = self.screen.get_height() - len(debug_text) * 20 - 10

        # Fondo semi-transparente
        debug_surface = pygame.Surface((240, len(debug_text) * 20 + 10), pygame.SRCALPHA)
        debug_surface.fill((0, 0, 0, 180))  # Negro semi-transparente
        self.screen.blit(debug_surface, (x, y))

        # Texto
        for i, text in enumerate(debug_text):
            text_surface = self.font.render(text, True, (255, 255, 255))
            self.screen.blit(text_surface, (x + 10, y + 10 + i * 20))

    def get_average_age(self, world):
        """Calcula la edad media de los Nexus vivos.

        Args:
            world (World): Referencia al mundo de la simulación.

        Returns:
            float: Edad media.
        """
        alive_nexus = [n for n in world.entities if n.alive]
        if not alive_nexus:
            return 0

        total_age = sum(n.age for n in alive_nexus)
        return total_age / len(alive_nexus)

    def get_average_energy(self, world):
        """Calcula la energía media de los Nexus vivos.

        Args:
            world (World): Referencia al mundo de la simulación.

        Returns:
            float: Energía media.
        """
        alive_nexus = [n for n in world.entities if n.alive]
        if not alive_nexus:
            return 0

        total_energy = sum(n.energy for n in alive_nexus)
        return total_energy / len(alive_nexus)

    def get_all_knowledge(self, world):
        """Obtiene el conjunto de todos los conocimientos descubiertos.

        Args:
            world (World): Referencia al mundo de la simulación.

        Returns:
            set: Conjunto de conocimientos descubiertos.
        """
        all_knowledge = set()
        for nexus in world.entities:
            if nexus.alive:
                all_knowledge.update(nexus.knowledge)

        return all_knowledge

    def get_state_distribution(self, world):
        """Obtiene la distribución de estados de los Nexus vivos.

        Args:
            world (World): Referencia al mundo de la simulación.

        Returns:
            dict: Diccionario con conteo de estados.
        """
        states = {}
        for nexus in world.entities:
            if nexus.alive:
                if nexus.state in states:
                    states[nexus.state] += 1
                else:
                    states[nexus.state] = 1

        return states

    def render_save_dialog(self):
        """Renderiza el diálogo de guardado de partida."""
        # Fondo semi-transparente
        panel_width = 400
        panel_height = 150
        x = (self.screen.get_width() - panel_width) // 2
        y = (self.screen.get_height() - panel_height) // 2

        panel = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        panel.fill((30, 30, 30, 230))  # Fondo oscuro semi-transparente
        pygame.draw.rect(panel, (255, 255, 255), (0, 0, panel_width, panel_height), 2)

        # Título
        title = self.font.render("Guardar Partida", True, (255, 255, 255))
        panel.blit(title, ((panel_width - title.get_width()) // 2, 20))

        # Campo de entrada
        pygame.draw.rect(panel, (50, 50, 50), (50, 60, panel_width - 100, 30))
        pygame.draw.rect(panel, (200, 200, 200), (50, 60, panel_width - 100, 30), 1)

        # Texto del nombre de archivo
        filename_text = self.font.render(self.save_filename, True, (255, 255, 255))
        panel.blit(filename_text, (60, 65))

        # Instrucciones
        instructions = self.font.render("Presiona Enter para guardar, Esc para cancelar", True, (200, 200, 200))
        panel.blit(instructions, ((panel_width - instructions.get_width()) // 2, 100))

        self.screen.blit(panel, (x, y))

    def render_load_dialog(self):
        """Renderiza el diálogo de carga de partida."""
        # Fondo semi-transparente
        panel_width = 500
        panel_height = min(400, len(self.save_files) * 30 + 100)
        x = (self.screen.get_width() - panel_width) // 2
        y = (self.screen.get_height() - panel_height) // 2

        panel = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        panel.fill((30, 30, 30, 230))  # Fondo oscuro semi-transparente
        pygame.draw.rect(panel, (255, 255, 255), (0, 0, panel_width, panel_height), 2)

        # Título
        title = self.font.render("Cargar Partida", True, (255, 255, 255))
        panel.blit(title, ((panel_width - title.get_width()) // 2, 20))

        # Instrucciones
        instructions = self.font.render("Presiona Enter para cargar, Esc para cancelar", True, (200, 200, 200))
        panel.blit(instructions, ((panel_width - instructions.get_width()) // 2, 50))

        # Lista de archivos de guardado
        if not self.save_files:
            no_files = self.font.render("No hay partidas guardadas", True, (200, 200, 200))
            panel.blit(no_files, ((panel_width - no_files.get_width()) // 2, panel_height // 2))
        else:
            for i, save_file in enumerate(self.save_files):
                # Resaltar seleccionado
                if i == self.selected_save_file:
                    pygame.draw.rect(panel, (100, 100, 255), (20, 80 + i * 30, panel_width - 40, 25))

                # Información del archivo
                file_info = f"{save_file['filename']} - {save_file['date']} ({save_file['size']} KB)"
                text = self.font.render(file_info, True, (255, 255, 255))
                panel.blit(text, (30, 80 + i * 30))

        self.screen.blit(panel, (x, y))

    def render_exit_dialog(self):
        """Renderiza el diálogo de confirmación para salir del juego."""
        # Dimensiones y posición del diálogo
        dialog_width = 400
        dialog_height = 200
        dialog_x = (self.screen.get_width() - dialog_width) // 2
        dialog_y = (self.screen.get_height() - dialog_height) // 2

        # Fondo semi-transparente para toda la pantalla
        overlay = pygame.Surface((self.screen.get_width(), self.screen.get_height()), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # Negro semi-transparente
        self.screen.blit(overlay, (0, 0))

        # Panel del diálogo
        dialog = pygame.Surface((dialog_width, dialog_height), pygame.SRCALPHA)
        dialog.fill((50, 50, 60, 240))  # Fondo del diálogo
        pygame.draw.rect(dialog, (200, 200, 220), (0, 0, dialog_width, dialog_height), 2)  # Borde

        # Título
        title_font = pygame.font.SysFont("Arial", 24, bold=True)
        title = title_font.render("¿Salir del juego?", True, (255, 255, 255))
        dialog.blit(title, ((dialog_width - title.get_width()) // 2, 20))

        # Mensaje
        message_font = pygame.font.SysFont("Arial", 18)
        message1 = message_font.render("¿Estás seguro de que quieres salir?", True, (255, 255, 255))
        message2 = message_font.render("Se guardará automáticamente la partida.", True, (255, 255, 255))
        dialog.blit(message1, ((dialog_width - message1.get_width()) // 2, 60))
        dialog.blit(message2, ((dialog_width - message2.get_width()) // 2, 85))

        # Botones
        # Botón Sí
        yes_rect = pygame.Rect(80, 120, 100, 40)
        pygame.draw.rect(dialog, (100, 150, 100), yes_rect)  # Verde
        pygame.draw.rect(dialog, (200, 255, 200), yes_rect, 2)  # Borde
        yes_text = self.font.render("Sí", True, (255, 255, 255))
        dialog.blit(yes_text, (yes_rect.centerx - yes_text.get_width() // 2, yes_rect.centery - yes_text.get_height() // 2))

        # Botón No
        no_rect = pygame.Rect(220, 120, 100, 40)
        pygame.draw.rect(dialog, (150, 100, 100), no_rect)  # Rojo
        pygame.draw.rect(dialog, (255, 200, 200), no_rect, 2)  # Borde
        no_text = self.font.render("No", True, (255, 255, 255))
        dialog.blit(no_text, (no_rect.centerx - no_text.get_width() // 2, no_rect.centery - no_text.get_height() // 2))

        # Renderizar diálogo
        self.screen.blit(dialog, (dialog_x, dialog_y))

    def render_universe25_metrics(self, world):
        """Renderiza métricas específicas del experimento Universe 25.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        if not hasattr(world, "stress_psychology_system"):
            return

        # Obtener reporte poblacional
        report = world.stress_psychology_system.get_population_report()

        # Información Universe 25
        universe25_text = [
            "=== MÉTRICAS UNIVERSE 25 ===",
            f"Población total: {report['total_population']}",
            f"Colapso social: {report['social_breakdown_percentage']:.1f}%",
            "",
            "Distribución de estrés:",
            f"  Bajo: {report['stress_distribution'].get('low', 0):.1f}%",
            f"  Moderado: {report['stress_distribution'].get('moderate', 0):.1f}%",
            f"  Alto: {report['stress_distribution'].get('high', 0):.1f}%",
            f"  Crítico: {report['stress_distribution'].get('critical', 0):.1f}%",
            "",
            "Comportamientos anómalos:",
            f"  Beautiful Ones: {report['anomalous_behaviors'].get('beautiful_ones', 0):.1f}%",
            f"  Agresivos: {report['anomalous_behaviors'].get('aggressive_dominants', 0):.1f}%",
            f"  Retraídos: {report['anomalous_behaviors'].get('passive_withdrawn', 0):.1f}%",
            f"  Hiperactividad: {report['anomalous_behaviors'].get('hyperactive_wanderers', 0):.1f}%",
            "",
            "Indicadores críticos:",
            f"  Estrés alto: {report['critical_indicators']['high_stress_population']:.1f}%",
            f"  Retiro social: {report['critical_indicators']['social_withdrawal']:.1f}%",
            f"  Agresividad: {report['critical_indicators']['aggressive_behavior']:.1f}%"
        ]

        # Determinar color de alerta
        if world.stress_psychology_system.is_population_collapsing():
            alert_color = (255, 100, 100)  # Rojo - colapso
            universe25_text.insert(1, "⚠️ COLAPSO POBLACIONAL DETECTADO ⚠️")
        elif report['social_breakdown_percentage'] > 40:
            alert_color = (255, 200, 100)  # Amarillo - advertencia
            universe25_text.insert(1, "⚠️ ALTA TENSIÓN SOCIAL ⚠️")
        else:
            alert_color = (100, 255, 100)  # Verde - estable
            universe25_text.insert(1, "✓ Población estable")

        # Renderizar en esquina inferior izquierda
        panel_width = 280
        panel_height = len(universe25_text) * 18 + 20
        x = 10
        y = self.screen.get_height() - panel_height - 10

        # Fondo semi-transparente
        universe25_surface = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        universe25_surface.fill((20, 20, 30, 200))
        pygame.draw.rect(universe25_surface, alert_color, (0, 0, panel_width, panel_height), 2)
        self.screen.blit(universe25_surface, (x, y))

        # Texto
        for i, text in enumerate(universe25_text):
            if i == 0:  # Título
                color = (255, 255, 255)
                font = self.font
            elif i == 1:  # Estado de alerta
                color = alert_color
                font = self.font
            elif text.startswith("  "):  # Subcategorías
                color = (200, 200, 200)
                font = self.font
            else:  # Categorías principales
                color = (220, 220, 220)
                font = self.font

            text_surface = font.render(text, True, color)
            self.screen.blit(text_surface, (x + 10, y + 10 + i * 18))

        # Información adicional de territorio si está disponible
        if hasattr(world, "territory_system") and world.territory_system.territories:
            territory_count = len(world.territory_system.territories)
            territory_text = f"Territorios activos: {territory_count}"

            # Renderizar información de territorio
            territory_surface = self.font.render(territory_text, True, (150, 200, 255))
            self.screen.blit(territory_surface, (x + 10, y + panel_height + 5))


class Button:
    """Clase que representa un botón en la interfaz."""

    def __init__(self, x, y, width, height, text, config, category=None):
        """Inicializa un nuevo botón.

        Args:
            x (int): Posición X del botón.
            y (int): Posición Y del botón.
            width (int): Ancho del botón.
            height (int): Alto del botón.
            text (str): Texto del botón.
            config (Config): Configuración del simulador.
            category (str, optional): Categoría a la que pertenece el botón.
        """
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.config = config
        self.font = config.FONT
        self.hover = False
        self.category = category
        self.visible = True
        self.original_position = (x, y)

    def set_position(self, x, y):
        """Actualiza la posición del botón.

        Args:
            x (int): Nueva posición X.
            y (int): Nueva posición Y.
        """
        self.rect.x = x
        self.rect.y = y

    def is_clicked(self, mouse_pos):
        """Verifica si el botón ha sido clicado.

        Args:
            mouse_pos (tuple): Posición del ratón (x, y).

        Returns:
            bool: True si el botón fue clicado, False en caso contrario.
        """
        return self.visible and self.rect.collidepoint(mouse_pos)

    def render(self, screen):
        """Renderiza el botón en la pantalla.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
        """
        if not self.visible:
            return

        # Verificar si el ratón está sobre el botón
        mouse_pos = pygame.mouse.get_pos()
        self.hover = self.rect.collidepoint(mouse_pos)

        # Color de fondo según estado
        if self.hover:
            bg_color = (80, 80, 100, 230)  # Resaltado semi-transparente
        else:
            bg_color = (50, 50, 70, 200)  # Normal semi-transparente

        # Dibujar fondo
        button_surface = pygame.Surface((self.rect.width, self.rect.height), pygame.SRCALPHA)
        button_surface.fill(bg_color)

        # Dibujar borde
        pygame.draw.rect(button_surface, self.config.UI_BORDER_COLOR,
                        (0, 0, self.rect.width, self.rect.height), self.config.UI_BORDER_WIDTH)

        # Renderizar texto
        text_surface = self.font.render(self.text, True, self.config.UI_TEXT_COLOR)
        text_rect = text_surface.get_rect(center=(self.rect.width // 2, self.rect.height // 2))
        button_surface.blit(text_surface, text_rect)

        # Dibujar en pantalla
        screen.blit(button_surface, self.rect)