#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON>lo de renderizado avanzado para Universe 25.

Este módulo contiene sistemas de renderizado mejorados incluyendo:
- Renderizador de Nexus con sprites y animaciones
- Sistema de partículas para efectos visuales
- Mapas de calor para análisis de datos
"""

from .nexus_renderer import NexusRenderer
from .particle_system import ParticleSystem
from .heatmap_renderer import HeatmapRenderer

__all__ = ['NexusRenderer', 'ParticleSystem', 'HeatmapRenderer']
