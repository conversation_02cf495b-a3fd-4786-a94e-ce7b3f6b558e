#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de territorio y competencia para el simulador Nexus.

Implementa mecánicas de territorialidad, competencia por recursos,
jerarquías sociales y conflictos territoriales.
"""

import random
import math
from collections import defaultdict

class TerritorySystem:
    """Sistema que gestiona territorios, jerarquías y competencia."""
    
    def __init__(self, config):
        """Inicializa el sistema de territorio.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Territorios activos
        self.territories = {}  # {territory_id: Territory}
        self.territory_id_counter = 0
        
        # Jerarquías sociales
        self.social_hierarchies = {}  # {group_id: Hierarchy}
        
        # Configuración de territorio
        self.territory_config = {
            "min_size": 50,      # Radio mínimo de territorio
            "max_size": 300,     # Radio máximo de territorio
            "establishment_energy": 40,  # Energía mínima para establecer territorio
            "defense_radius": 150,       # Radio de defensa activa
            "patrol_frequency": 0.1,     # Probabilidad de patrullar por tick
            "marking_duration": 1000     # Duración de marcas territoriales
        }
        
        # Tipos de jerarquía social
        self.hierarchy_types = {
            "alpha_beta_omega": {
                "levels": ["alpha", "beta", "gamma", "omega"],
                "max_alphas": 1,
                "promotion_threshold": 0.8,
                "demotion_threshold": 0.3
            },
            "dominance_chain": {
                "levels": ["dominant", "subdominant", "subordinate"],
                "max_dominants": 2,
                "promotion_threshold": 0.7,
                "demotion_threshold": 0.4
            }
        }
        
        # Factores de dominancia
        self.dominance_factors = {
            "physical": ["strength", "health", "size"],
            "social": ["charisma", "social_connections"],
            "resource": ["territory_size", "resource_control"],
            "experience": ["age", "survival_time", "knowledge_count"]
        }
    
    def update(self, world):
        """Actualiza el sistema de territorio para todos los Nexus.
        
        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Actualizar territorios existentes
        self._update_territories(world)
        
        # Evaluar establecimiento de nuevos territorios
        self._evaluate_territory_establishment(world)
        
        # Actualizar jerarquías sociales
        self._update_social_hierarchies(world)
        
        # Gestionar conflictos territoriales
        self._handle_territorial_conflicts(world)
        
        # Actualizar comportamientos territoriales
        self._update_territorial_behaviors(world)
    
    def _update_territories(self, world):
        """Actualiza territorios existentes.
        
        Args:
            world (World): Referencia al mundo.
        """
        for territory_id, territory in list(self.territories.items()):
            # Verificar si el dueño sigue vivo
            if not territory.owner.alive:
                del self.territories[territory_id]
                continue
            
            # Actualizar marcas territoriales
            territory.update_markings()
            
            # Verificar intrusos
            intruders = self._detect_intruders(territory, world)
            if intruders:
                territory.handle_intrusion(intruders, world)
    
    def _evaluate_territory_establishment(self, world):
        """Evalúa si algún Nexus debería establecer territorio.
        
        Args:
            world (World): Referencia al mundo.
        """
        for nexus in world.entities:
            if not nexus.alive:
                continue
            
            # Verificar si ya tiene territorio
            if self._has_territory(nexus):
                continue
            
            # Verificar condiciones para establecer territorio
            if self._should_establish_territory(nexus, world):
                self._establish_territory(nexus, world)
    
    def _should_establish_territory(self, nexus, world):
        """Determina si un Nexus debería establecer territorio.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            world (World): Referencia al mundo.
            
        Returns:
            bool: True si debería establecer territorio.
        """
        # Condiciones básicas
        if nexus.energy < self.territory_config["establishment_energy"]:
            return False
        
        # Personalidad territorial
        territoriality = nexus.personality.get("territoriality", 0.5)
        if territoriality < 0.4:
            return False
        
        # Evaluar recursos en el área
        nearby_resources = world.get_nearby_resources(nexus.position, 200)
        resource_density = len(nearby_resources)
        
        # Evaluar competencia
        nearby_nexus = world.get_nearby_nexus(nexus.position, 200, exclude=nexus)
        competition = len(nearby_nexus)
        
        # Calcular probabilidad de establecimiento
        establishment_chance = (
            territoriality * 0.4 +
            min(1.0, resource_density / 10) * 0.3 +
            max(0.0, 1.0 - competition / 5) * 0.3
        )
        
        return random.random() < establishment_chance * 0.1  # 10% máximo por tick
    
    def _establish_territory(self, nexus, world):
        """Establece un territorio para un Nexus.
        
        Args:
            nexus (Nexus): Nexus que establece territorio.
            world (World): Referencia al mundo.
        """
        # Calcular tamaño del territorio basado en dominancia
        dominance_score = self._calculate_dominance_score(nexus)
        territory_size = (
            self.territory_config["min_size"] + 
            (self.territory_config["max_size"] - self.territory_config["min_size"]) * dominance_score
        )
        
        # Crear territorio
        territory = Territory(
            self.territory_id_counter,
            nexus,
            nexus.position.copy(),
            territory_size,
            self.territory_config
        )
        
        self.territories[self.territory_id_counter] = territory
        self.territory_id_counter += 1
        
        # Actualizar Nexus
        nexus.territory_id = territory.id
        nexus.territory_center = territory.center.copy()
        nexus.territory_size = territory.size
        
        # Consumir energía
        nexus.energy -= self.territory_config["establishment_energy"] * 0.5
        
        # Añadir memoria
        nexus.add_memory(f"Estableció territorio de {int(territory_size)} unidades de radio")
        
        # Cambiar estado
        nexus.state = "establishing_territory"
    
    def _calculate_dominance_score(self, nexus):
        """Calcula el puntaje de dominancia de un Nexus.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            
        Returns:
            float: Puntaje de dominancia (0-1).
        """
        score = 0.0
        factor_count = 0
        
        # Factores físicos
        physical_score = (
            nexus.attributes.get("strength", 0.5) * 0.4 +
            (nexus.health / 100) * 0.3 +
            min(1.0, nexus.size / 30) * 0.3
        )
        score += physical_score
        factor_count += 1
        
        # Factores sociales
        social_connections = len([r for r in nexus.relationships.values() if r > 0.6])
        social_score = (
            nexus.attributes.get("charisma", 0.5) * 0.6 +
            min(1.0, social_connections / 5) * 0.4
        )
        score += social_score
        factor_count += 1
        
        # Factores de experiencia
        experience_score = (
            min(1.0, nexus.age / 10000) * 0.4 +
            min(1.0, len(nexus.knowledge) / 20) * 0.6
        )
        score += experience_score
        factor_count += 1
        
        return score / factor_count
    
    def _detect_intruders(self, territory, world):
        """Detecta intrusos en un territorio.
        
        Args:
            territory (Territory): Territorio a verificar.
            world (World): Referencia al mundo.
            
        Returns:
            list: Lista de Nexus intrusos.
        """
        intruders = []
        
        for nexus in world.entities:
            if not nexus.alive or nexus == territory.owner:
                continue
            
            # Calcular distancia al centro del territorio
            distance = math.sqrt(
                (nexus.position[0] - territory.center[0])**2 +
                (nexus.position[1] - territory.center[1])**2
            )
            
            # Verificar si está dentro del territorio
            if distance <= territory.size:
                # Verificar si tiene permiso (aliados, familia, etc.)
                if not self._has_territory_permission(nexus, territory):
                    intruders.append(nexus)
        
        return intruders
    
    def _has_territory_permission(self, nexus, territory):
        """Verifica si un Nexus tiene permiso para estar en el territorio.
        
        Args:
            nexus (Nexus): Nexus a verificar.
            territory (Territory): Territorio en cuestión.
            
        Returns:
            bool: True si tiene permiso.
        """
        # Miembros de la misma tribu
        if (hasattr(nexus, "tribe") and hasattr(territory.owner, "tribe") and
            nexus.tribe and nexus.tribe == territory.owner.tribe):
            return True
        
        # Relación positiva fuerte
        relationship = nexus.relationships.get(territory.owner.name, 0.0)
        if relationship > 0.7:
            return True
        
        # Familia (padres, hijos, hermanos)
        if (hasattr(nexus, "family") and hasattr(territory.owner, "family") and
            nexus.family and territory.owner.family):
            if nexus.name in territory.owner.family or territory.owner.name in nexus.family:
                return True
        
        return False
    
    def _update_social_hierarchies(self, world):
        """Actualiza las jerarquías sociales.
        
        Args:
            world (World): Referencia al mundo.
        """
        # Agrupar Nexus por tribu o proximidad
        groups = self._group_nexus_for_hierarchy(world)
        
        for group_id, group_members in groups.items():
            if len(group_members) < 3:  # Mínimo para jerarquía
                continue
            
            # Crear o actualizar jerarquía
            if group_id not in self.social_hierarchies:
                self.social_hierarchies[group_id] = SocialHierarchy(
                    group_id, group_members, self.hierarchy_types["alpha_beta_omega"]
                )
            else:
                self.social_hierarchies[group_id].update_hierarchy(group_members)
    
    def _group_nexus_for_hierarchy(self, world):
        """Agrupa Nexus para establecer jerarquías.
        
        Args:
            world (World): Referencia al mundo.
            
        Returns:
            dict: Grupos de Nexus {group_id: [nexus_list]}.
        """
        groups = defaultdict(list)
        
        for nexus in world.entities:
            if not nexus.alive:
                continue
            
            # Agrupar por tribu si existe
            if hasattr(nexus, "tribe") and nexus.tribe:
                groups[f"tribe_{nexus.tribe.id}"].append(nexus)
            else:
                # Agrupar por proximidad geográfica
                sector_x = int(nexus.position[0] / 500)
                sector_y = int(nexus.position[1] / 500)
                groups[f"sector_{sector_x}_{sector_y}"].append(nexus)
        
        return groups
    
    def _handle_territorial_conflicts(self, world):
        """Gestiona conflictos territoriales.
        
        Args:
            world (World): Referencia al mundo.
        """
        for territory in self.territories.values():
            if territory.active_conflicts:
                self._resolve_territorial_conflicts(territory, world)
    
    def _resolve_territorial_conflicts(self, territory, world):
        """Resuelve conflictos territoriales específicos.
        
        Args:
            territory (Territory): Territorio en conflicto.
            world (World): Referencia al mundo.
        """
        for conflict in list(territory.active_conflicts):
            intruder = conflict["intruder"]
            
            if not intruder.alive:
                territory.active_conflicts.remove(conflict)
                continue
            
            # Determinar resultado del conflicto
            owner_strength = self._calculate_combat_strength(territory.owner)
            intruder_strength = self._calculate_combat_strength(intruder)
            
            # Factores adicionales
            home_advantage = 1.2  # Ventaja del defensor
            owner_strength *= home_advantage
            
            # Resolver conflicto
            if owner_strength > intruder_strength:
                # El dueño gana, expulsar intruso
                self._expel_intruder(territory, intruder, world)
            else:
                # El intruso gana, puede tomar territorio
                self._transfer_territory(territory, intruder, world)
            
            territory.active_conflicts.remove(conflict)
    
    def _calculate_combat_strength(self, nexus):
        """Calcula la fuerza de combate de un Nexus.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            
        Returns:
            float: Fuerza de combate.
        """
        base_strength = (
            nexus.attributes.get("strength", 0.5) * 0.4 +
            (nexus.health / 100) * 0.3 +
            (nexus.energy / 100) * 0.3
        )
        
        # Bonus por herramientas de combate
        tool_bonus = 1.0
        if hasattr(nexus, "inventory") and "tools" in nexus.inventory:
            for tool in nexus.inventory["tools"]:
                if tool.get("type") in ["spear", "stone_axe"]:
                    tool_bonus += 0.3
        
        # Bonus por experiencia en combate
        experience_bonus = 1.0
        if hasattr(nexus, "combat_experience"):
            experience_bonus += nexus.combat_experience * 0.1
        
        return base_strength * tool_bonus * experience_bonus
    
    def _expel_intruder(self, territory, intruder, world):
        """Expulsa un intruso del territorio.
        
        Args:
            territory (Territory): Territorio defendido.
            intruder (Nexus): Intruso a expulsar.
            world (World): Referencia al mundo.
        """
        # Calcular dirección de expulsión
        dx = intruder.position[0] - territory.center[0]
        dy = intruder.position[1] - territory.center[1]
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance > 0:
            # Normalizar y extender
            dx /= distance
            dy /= distance
            
            # Mover fuera del territorio
            expulsion_distance = territory.size + 50
            new_x = territory.center[0] + dx * expulsion_distance
            new_y = territory.center[1] + dy * expulsion_distance
            
            intruder.position[0] = new_x
            intruder.position[1] = new_y
        
        # Efectos en el intruso
        intruder.health -= random.uniform(5, 15)
        intruder.emotions["fear"] = min(1.0, intruder.emotions.get("fear", 0) + 0.3)
        intruder.add_memory(f"Fue expulsado del territorio de {territory.owner.name}")
        
        # Efectos en el dueño
        territory.owner.add_memory(f"Defendió exitosamente su territorio contra {intruder.name}")
        if not hasattr(territory.owner, "combat_experience"):
            territory.owner.combat_experience = 0
        territory.owner.combat_experience += 0.1
    
    def _transfer_territory(self, territory, new_owner, world):
        """Transfiere un territorio a un nuevo dueño.
        
        Args:
            territory (Territory): Territorio a transferir.
            new_owner (Nexus): Nuevo dueño.
            world (World): Referencia al mundo.
        """
        old_owner = territory.owner
        
        # Efectos en el antiguo dueño
        old_owner.health -= random.uniform(10, 25)
        old_owner.emotions["anger"] = min(1.0, old_owner.emotions.get("anger", 0) + 0.4)
        old_owner.emotions["fear"] = min(1.0, old_owner.emotions.get("fear", 0) + 0.2)
        old_owner.add_memory(f"Perdió su territorio ante {new_owner.name}")
        
        # Limpiar referencias del antiguo dueño
        if hasattr(old_owner, "territory_id"):
            delattr(old_owner, "territory_id")
        if hasattr(old_owner, "territory_center"):
            delattr(old_owner, "territory_center")
        if hasattr(old_owner, "territory_size"):
            delattr(old_owner, "territory_size")
        
        # Transferir al nuevo dueño
        territory.owner = new_owner
        new_owner.territory_id = territory.id
        new_owner.territory_center = territory.center.copy()
        new_owner.territory_size = territory.size
        
        # Efectos en el nuevo dueño
        new_owner.add_memory(f"Conquistó el territorio de {old_owner.name}")
        if not hasattr(new_owner, "combat_experience"):
            new_owner.combat_experience = 0
        new_owner.combat_experience += 0.2
    
    def _update_territorial_behaviors(self, world):
        """Actualiza comportamientos territoriales.
        
        Args:
            world (World): Referencia al mundo.
        """
        for nexus in world.entities:
            if not nexus.alive:
                continue
            
            # Comportamiento de patrullaje
            if (self._has_territory(nexus) and 
                random.random() < self.territory_config["patrol_frequency"]):
                
                if nexus.state not in ["fleeing", "seeking_food", "seeking_water"]:
                    nexus.state = "patrolling"
                    
                    # Generar punto de patrullaje
                    angle = random.uniform(0, 2 * math.pi)
                    distance = random.uniform(nexus.territory_size * 0.5, nexus.territory_size)
                    patrol_x = nexus.territory_center[0] + math.cos(angle) * distance
                    patrol_y = nexus.territory_center[1] + math.sin(angle) * distance
                    nexus.target = [patrol_x, patrol_y]
    
    def _has_territory(self, nexus):
        """Verifica si un Nexus tiene territorio.
        
        Args:
            nexus (Nexus): Nexus a verificar.
            
        Returns:
            bool: True si tiene territorio.
        """
        return (hasattr(nexus, "territory_id") and 
                nexus.territory_id in self.territories)


class Territory:
    """Representa un territorio individual."""
    
    def __init__(self, territory_id, owner, center, size, config):
        """Inicializa un territorio.
        
        Args:
            territory_id (int): ID único del territorio.
            owner (Nexus): Dueño del territorio.
            center (list): Centro del territorio [x, y].
            size (float): Radio del territorio.
            config (dict): Configuración del territorio.
        """
        self.id = territory_id
        self.owner = owner
        self.center = center
        self.size = size
        self.config = config
        
        # Estado del territorio
        self.markings = []  # Marcas territoriales
        self.active_conflicts = []  # Conflictos activos
        self.last_patrol_time = 0
        self.establishment_time = owner.age if hasattr(owner, "age") else 0
    
    def update_markings(self):
        """Actualiza las marcas territoriales."""
        # Remover marcas expiradas
        current_time = self.owner.age if hasattr(self.owner, "age") else 0
        self.markings = [
            mark for mark in self.markings 
            if current_time - mark["time"] < self.config["marking_duration"]
        ]
    
    def handle_intrusion(self, intruders, world):
        """Maneja intrusiones en el territorio.
        
        Args:
            intruders (list): Lista de intrusos.
            world (World): Referencia al mundo.
        """
        for intruder in intruders:
            # Verificar si ya hay conflicto activo
            existing_conflict = any(
                conflict["intruder"] == intruder 
                for conflict in self.active_conflicts
            )
            
            if not existing_conflict:
                # Crear nuevo conflicto
                conflict = {
                    "intruder": intruder,
                    "start_time": self.owner.age if hasattr(self.owner, "age") else 0,
                    "intensity": 1.0
                }
                self.active_conflicts.append(conflict)
                
                # Cambiar estado del dueño a defensa
                if self.owner.state not in ["fleeing"]:
                    self.owner.state = "defending"
                    self.owner.target = intruder.position.copy()


class SocialHierarchy:
    """Representa una jerarquía social."""
    
    def __init__(self, group_id, members, hierarchy_type):
        """Inicializa una jerarquía social.
        
        Args:
            group_id (str): ID del grupo.
            members (list): Miembros del grupo.
            hierarchy_type (dict): Tipo de jerarquía.
        """
        self.group_id = group_id
        self.members = members
        self.hierarchy_type = hierarchy_type
        self.rankings = {}  # {nexus: rank}
        
        # Establecer jerarquía inicial
        self._establish_initial_hierarchy()
    
    def _establish_initial_hierarchy(self):
        """Establece la jerarquía inicial basada en dominancia."""
        # Calcular puntajes de dominancia para todos los miembros
        dominance_scores = []
        for member in self.members:
            score = self._calculate_member_dominance(member)
            dominance_scores.append((member, score))
        
        # Ordenar por dominancia
        dominance_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Asignar rangos
        levels = self.hierarchy_type["levels"]
        members_per_level = max(1, len(self.members) // len(levels))
        
        for i, (member, score) in enumerate(dominance_scores):
            level_index = min(i // members_per_level, len(levels) - 1)
            self.rankings[member] = levels[level_index]
            
            # Actualizar atributos del miembro
            if not hasattr(member, "social_rank"):
                member.social_rank = levels[level_index]
            else:
                member.social_rank = levels[level_index]
    
    def _calculate_member_dominance(self, member):
        """Calcula la dominancia de un miembro.
        
        Args:
            member (Nexus): Miembro a evaluar.
            
        Returns:
            float: Puntaje de dominancia.
        """
        # Factores físicos
        physical = (
            member.attributes.get("strength", 0.5) * 0.3 +
            (member.health / 100) * 0.2 +
            (member.energy / 100) * 0.1
        )
        
        # Factores sociales
        social_connections = len([r for r in member.relationships.values() if r > 0.6])
        social = min(1.0, social_connections / 5) * 0.2
        
        # Factores de experiencia
        experience = (
            min(1.0, member.age / 10000) * 0.1 +
            min(1.0, len(member.knowledge) / 20) * 0.1
        )
        
        return physical + social + experience
    
    def update_hierarchy(self, current_members):
        """Actualiza la jerarquía con los miembros actuales.
        
        Args:
            current_members (list): Miembros actuales del grupo.
        """
        self.members = current_members
        
        # Recalcular si hay cambios significativos
        if len(current_members) != len(self.rankings):
            self._establish_initial_hierarchy()
        else:
            # Verificar promociones/degradaciones
            self._check_rank_changes()
