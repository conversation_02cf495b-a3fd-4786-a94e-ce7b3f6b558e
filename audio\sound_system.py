#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de sonido y música para la simulación Universe 25.

Proporciona efectos de sonido dinámicos y música adaptativa
basada en el estado de la población y eventos de la simulación.
"""

import pygame
import random
import math
import os
from collections import defaultdict

class SoundSystem:
    """Sistema de sonido y música adaptativo."""
    
    def __init__(self, config):
        """Inicializa el sistema de sonido.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        self.enabled = True
        self.volume = 0.7
        self.music_volume = 0.5
        self.sfx_volume = 0.8
        
        # Inicializar pygame mixer
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.mixer_available = True
        except pygame.error:
            print("Warning: No se pudo inicializar el sistema de audio")
            self.mixer_available = False
            self.enabled = False
        
        # Canales de sonido
        self.channels = {
            "ambient": pygame.mixer.Channel(0) if self.mixer_available else None,
            "effects": pygame.mixer.Channel(1) if self.mixer_available else None,
            "music": pygame.mixer.Channel(2) if self.mixer_available else None,
            "ui": pygame.mixer.Channel(3) if self.mixer_available else None
        }
        
        # Sonidos generados proceduralmente
        self.generated_sounds = {}
        self.current_music_state = "peaceful"
        self.music_transition_timer = 0
        
        # Estados de música basados en la población
        self.music_states = {
            "peaceful": {"tempo": 60, "tension": 0.0, "complexity": 0.3},
            "growing": {"tempo": 80, "tension": 0.2, "complexity": 0.5},
            "tense": {"tempo": 100, "tension": 0.6, "complexity": 0.7},
            "chaotic": {"tempo": 120, "tension": 0.9, "complexity": 0.9},
            "collapse": {"tempo": 40, "tension": 1.0, "complexity": 0.2}
        }
        
        # Generar sonidos básicos (solo si numpy está disponible)
        if self.mixer_available:
            try:
                self._generate_basic_sounds()
            except Exception as e:
                print(f"Warning: No se pudieron generar sonidos básicos: {e}")
                self.enabled = False
    
    def _generate_basic_sounds(self):
        """Genera sonidos básicos proceduralmente."""
        # Generar tonos básicos para diferentes eventos
        sample_rate = 22050
        
        # Sonido de nacimiento (tono ascendente)
        birth_sound = self._generate_tone_sweep(440, 880, 0.5, sample_rate)
        self.generated_sounds["birth"] = pygame.sndarray.make_sound(birth_sound)
        
        # Sonido de muerte (tono descendente)
        death_sound = self._generate_tone_sweep(440, 220, 1.0, sample_rate)
        self.generated_sounds["death"] = pygame.sndarray.make_sound(death_sound)
        
        # Sonido de estrés (ruido modulado)
        stress_sound = self._generate_stress_sound(0.3, sample_rate)
        self.generated_sounds["stress"] = pygame.sndarray.make_sound(stress_sound)
        
        # Sonido de interacción social (armonía)
        social_sound = self._generate_harmony([440, 554, 659], 0.4, sample_rate)
        self.generated_sounds["social"] = pygame.sndarray.make_sound(social_sound)
        
        # Sonido de conflicto (disonancia)
        conflict_sound = self._generate_dissonance([440, 466, 493], 0.6, sample_rate)
        self.generated_sounds["conflict"] = pygame.sndarray.make_sound(conflict_sound)
        
        # Sonido de territorio (tono profundo)
        territory_sound = self._generate_tone(220, 0.8, sample_rate)
        self.generated_sounds["territory"] = pygame.sndarray.make_sound(territory_sound)
        
        # Sonidos de UI
        ui_click = self._generate_click_sound(sample_rate)
        self.generated_sounds["ui_click"] = pygame.sndarray.make_sound(ui_click)
        
        ui_hover = self._generate_tone(800, 0.1, sample_rate)
        self.generated_sounds["ui_hover"] = pygame.sndarray.make_sound(ui_hover)
    
    def _generate_tone(self, frequency, duration, sample_rate):
        """Genera un tono puro."""
        try:
            import numpy as np
            frames = int(duration * sample_rate)
            arr = np.zeros((frames, 2), dtype=np.float32)

            for i in range(frames):
                wave = np.sin(2 * np.pi * frequency * i / sample_rate)
                # Envelope para evitar clicks
                envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
                arr[i] = [wave * envelope * 0.3, wave * envelope * 0.3]

            return (arr * 32767).astype(np.int16)
        except ImportError:
            # Fallback sin numpy
            frames = int(duration * sample_rate)
            arr = []
            for i in range(frames):
                wave = math.sin(2 * math.pi * frequency * i / sample_rate)
                envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
                sample = int(wave * envelope * 0.3 * 32767)
                arr.append([sample, sample])
            return arr
    
    def _generate_tone_sweep(self, start_freq, end_freq, duration, sample_rate):
        """Genera un barrido de frecuencia."""
        try:
            import numpy as np
            frames = int(duration * sample_rate)
            arr = np.zeros((frames, 2), dtype=np.float32)

            for i in range(frames):
                progress = i / frames
                frequency = start_freq + (end_freq - start_freq) * progress
                wave = np.sin(2 * np.pi * frequency * i / sample_rate)
                # Envelope
                envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
                arr[i] = [wave * envelope * 0.3, wave * envelope * 0.3]

            return (arr * 32767).astype(np.int16)
        except ImportError:
            # Fallback sin numpy
            frames = int(duration * sample_rate)
            arr = []
            for i in range(frames):
                progress = i / frames
                frequency = start_freq + (end_freq - start_freq) * progress
                wave = math.sin(2 * math.pi * frequency * i / sample_rate)
                envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
                sample = int(wave * envelope * 0.3 * 32767)
                arr.append([sample, sample])
            return arr
    
    def _generate_stress_sound(self, duration, sample_rate):
        """Genera sonido de estrés (ruido modulado)."""
        import numpy as np
        frames = int(duration * sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            # Ruido blanco modulado
            noise = (random.random() - 0.5) * 2
            modulation = np.sin(2 * np.pi * 10 * i / sample_rate)  # 10 Hz modulation
            wave = noise * (0.5 + 0.5 * modulation)
            # Envelope
            envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
            arr[i] = [wave * envelope * 0.2, wave * envelope * 0.2]
        
        return (arr * 32767).astype(np.int16)
    
    def _generate_harmony(self, frequencies, duration, sample_rate):
        """Genera un acorde armónico."""
        import numpy as np
        frames = int(duration * sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            wave = 0
            for freq in frequencies:
                wave += np.sin(2 * np.pi * freq * i / sample_rate) / len(frequencies)
            # Envelope
            envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
            arr[i] = [wave * envelope * 0.3, wave * envelope * 0.3]
        
        return (arr * 32767).astype(np.int16)
    
    def _generate_dissonance(self, frequencies, duration, sample_rate):
        """Genera un acorde disonante."""
        import numpy as np
        frames = int(duration * sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            wave = 0
            for j, freq in enumerate(frequencies):
                # Añadir ligera desafinación para crear disonancia
                detuned_freq = freq * (1 + 0.02 * j)
                wave += np.sin(2 * np.pi * detuned_freq * i / sample_rate) / len(frequencies)
            # Envelope
            envelope = min(1.0, i / (sample_rate * 0.01), (frames - i) / (sample_rate * 0.01))
            arr[i] = [wave * envelope * 0.3, wave * envelope * 0.3]
        
        return (arr * 32767).astype(np.int16)
    
    def _generate_click_sound(self, sample_rate):
        """Genera sonido de click para UI."""
        import numpy as np
        frames = int(0.05 * sample_rate)  # 50ms
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            wave = np.sin(2 * np.pi * 1000 * i / sample_rate)
            envelope = max(0, 1 - i / frames)  # Decay rápido
            arr[i] = [wave * envelope * 0.2, wave * envelope * 0.2]
        
        return (arr * 32767).astype(np.int16)
    
    def play_sound(self, sound_name, volume=None, channel="effects"):
        """Reproduce un sonido.
        
        Args:
            sound_name (str): Nombre del sonido.
            volume (float, optional): Volumen (0.0-1.0).
            channel (str): Canal de audio a usar.
        """
        if not self.enabled or not self.mixer_available:
            return
        
        if sound_name not in self.generated_sounds:
            return
        
        sound = self.generated_sounds[sound_name]
        
        if volume is None:
            volume = self.sfx_volume
        
        sound.set_volume(volume)
        
        if channel in self.channels and self.channels[channel]:
            self.channels[channel].play(sound)
    
    def update_music_state(self, world):
        """Actualiza el estado de la música basado en la simulación.
        
        Args:
            world (World): Mundo de la simulación.
        """
        if not self.enabled or not hasattr(world, 'stress_psychology_system'):
            return
        
        # Obtener métricas de la población
        report = world.stress_psychology_system.get_population_report()
        population = report.get('total_population', 0)
        stress_breakdown = report.get('social_breakdown_percentage', 0)
        
        # Determinar nuevo estado musical
        new_state = "peaceful"
        
        if population == 0:
            new_state = "peaceful"
        elif stress_breakdown > 80:
            new_state = "collapse"
        elif stress_breakdown > 60:
            new_state = "chaotic"
        elif stress_breakdown > 30:
            new_state = "tense"
        elif population > 20:
            new_state = "growing"
        
        # Cambiar estado si es necesario
        if new_state != self.current_music_state:
            self.transition_to_music_state(new_state)
    
    def transition_to_music_state(self, new_state):
        """Transiciona a un nuevo estado musical.
        
        Args:
            new_state (str): Nuevo estado musical.
        """
        self.current_music_state = new_state
        self.music_transition_timer = 0
        
        # Generar nueva música adaptativa
        if self.mixer_available:
            self._generate_adaptive_music(new_state)
    
    def _generate_adaptive_music(self, state):
        """Genera música adaptativa para un estado.
        
        Args:
            state (str): Estado musical.
        """
        if state not in self.music_states:
            return
        
        state_data = self.music_states[state]
        
        # Generar música procedural basada en el estado
        # (Implementación simplificada - en un proyecto real usarías bibliotecas más avanzadas)
        sample_rate = 22050
        duration = 10.0  # 10 segundos de música
        
        music = self._generate_adaptive_track(
            state_data["tempo"],
            state_data["tension"],
            state_data["complexity"],
            duration,
            sample_rate
        )
        
        if music is not None:
            music_sound = pygame.sndarray.make_sound(music)
            music_sound.set_volume(self.music_volume)
            
            if self.channels["music"]:
                self.channels["music"].play(music_sound, loops=-1)  # Loop infinito
    
    def _generate_adaptive_track(self, tempo, tension, complexity, duration, sample_rate):
        """Genera una pista musical adaptativa."""
        try:
            import numpy as np
            frames = int(duration * sample_rate)
            arr = np.zeros((frames, 2))
            
            # Frecuencias base para la progresión
            base_frequencies = [220, 246.94, 277.18, 293.66, 329.63, 369.99, 415.30]  # A minor scale
            
            beat_length = int(sample_rate * 60 / tempo)  # Frames per beat
            
            for i in range(frames):
                beat_position = (i // beat_length) % 8  # 8-beat pattern
                
                # Seleccionar frecuencia basada en la complejidad
                if complexity > 0.7:
                    freq_index = beat_position % len(base_frequencies)
                elif complexity > 0.4:
                    freq_index = (beat_position // 2) % len(base_frequencies)
                else:
                    freq_index = 0  # Solo tónica
                
                frequency = base_frequencies[freq_index]
                
                # Generar onda
                wave = np.sin(2 * np.pi * frequency * i / sample_rate)
                
                # Añadir tensión con disonancia
                if tension > 0.5:
                    dissonant_freq = frequency * (1 + tension * 0.1)
                    wave += np.sin(2 * np.pi * dissonant_freq * i / sample_rate) * tension * 0.3
                
                # Envelope rítmico
                beat_progress = (i % beat_length) / beat_length
                envelope = 0.5 + 0.5 * np.sin(2 * np.pi * beat_progress)
                
                # Aplicar tensión al envelope
                envelope *= (1 - tension * 0.3)
                
                arr[i] = [wave * envelope * 0.1, wave * envelope * 0.1]
            
            return (arr * 32767).astype(np.int16)
        
        except ImportError:
            # Si numpy no está disponible, retornar None
            return None
    
    def play_event_sound(self, event_type, position=None, world=None):
        """Reproduce sonido para un evento específico.
        
        Args:
            event_type (str): Tipo de evento.
            position (tuple, optional): Posición del evento para audio espacial.
            world (World, optional): Mundo para cálculos espaciales.
        """
        volume = self.sfx_volume
        
        # Calcular volumen espacial si se proporciona posición
        if position and world and hasattr(world, 'camera_offset'):
            distance = math.sqrt(
                (position[0] - world.camera_offset[0])**2 +
                (position[1] - world.camera_offset[1])**2
            )
            # Reducir volumen con la distancia
            max_distance = 500
            volume *= max(0, 1 - distance / max_distance)
        
        # Mapear eventos a sonidos
        sound_map = {
            "birth": "birth",
            "death": "death",
            "stress": "stress",
            "social_positive": "social",
            "social_negative": "conflict",
            "territory_established": "territory",
            "conflict": "conflict",
            "ui_click": "ui_click",
            "ui_hover": "ui_hover"
        }
        
        sound_name = sound_map.get(event_type)
        if sound_name:
            channel = "ui" if event_type.startswith("ui_") else "effects"
            self.play_sound(sound_name, volume, channel)
    
    def set_volume(self, volume_type, volume):
        """Establece el volumen para un tipo específico.
        
        Args:
            volume_type (str): Tipo de volumen ('master', 'music', 'sfx').
            volume (float): Volumen (0.0-1.0).
        """
        volume = max(0.0, min(1.0, volume))
        
        if volume_type == "master":
            self.volume = volume
            pygame.mixer.set_volume(volume)
        elif volume_type == "music":
            self.music_volume = volume
        elif volume_type == "sfx":
            self.sfx_volume = volume
    
    def toggle_enabled(self):
        """Activa/desactiva el sistema de sonido."""
        self.enabled = not self.enabled
        if not self.enabled and self.mixer_available:
            pygame.mixer.stop()
    
    def cleanup(self):
        """Limpia recursos del sistema de sonido."""
        if self.mixer_available:
            pygame.mixer.quit()
