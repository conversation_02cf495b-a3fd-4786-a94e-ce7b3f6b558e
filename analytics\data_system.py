#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de análisis de datos y estadísticas para Universe 25.

Recopila, analiza y visualiza datos detallados sobre la simulación
para investigación y análisis científico.
"""

import json
import csv
import time
import math
from collections import defaultdict, deque
from datetime import datetime

class DataCollectionSystem:
    """Sistema de recopilación y análisis de datos científicos."""
    
    def __init__(self, config):
        """Inicializa el sistema de datos.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Datos históricos
        self.population_history = deque(maxlen=10000)
        self.stress_history = deque(maxlen=10000)
        self.behavior_history = deque(maxlen=10000)
        self.resource_history = deque(maxlen=10000)
        self.territory_history = deque(maxlen=10000)
        self.event_log = []
        
        # Métricas actuales
        self.current_metrics = {}
        self.session_start_time = time.time()
        self.simulation_tick = 0
        
        # Configuración de recopilación
        self.collection_interval = 10  # Recopilar cada 10 ticks
        self.detailed_logging = True
        self.export_enabled = True
        
        # Análisis estadístico
        self.statistical_analysis = {}
        self.correlation_data = {}
        self.trend_analysis = {}
        
        # Eventos significativos
        self.significant_events = []
        self.anomaly_detection = {
            "population_spikes": [],
            "stress_peaks": [],
            "behavioral_anomalies": [],
            "mass_deaths": []
        }
    
    def update(self, world):
        """Actualiza la recopilación de datos.
        
        Args:
            world (World): Mundo de la simulación.
        """
        self.simulation_tick += 1
        
        # Recopilar datos según el intervalo
        if self.simulation_tick % self.collection_interval == 0:
            self._collect_population_data(world)
            self._collect_stress_data(world)
            self._collect_behavior_data(world)
            self._collect_resource_data(world)
            self._collect_territory_data(world)
            self._update_current_metrics(world)
        
        # Detectar eventos significativos
        self._detect_significant_events(world)
        
        # Análisis en tiempo real
        if self.simulation_tick % 100 == 0:  # Cada 100 ticks
            self._perform_statistical_analysis()
            self._detect_anomalies()
    
    def _collect_population_data(self, world):
        """Recopila datos de población."""
        alive_entities = [e for e in world.entities if e.alive]
        
        population_data = {
            "timestamp": self.simulation_tick,
            "total_population": len(alive_entities),
            "male_count": len([e for e in alive_entities if e.sex == "male"]),
            "female_count": len([e for e in alive_entities if e.sex == "female"]),
            "age_distribution": self._calculate_age_distribution(alive_entities),
            "health_distribution": self._calculate_health_distribution(alive_entities),
            "energy_distribution": self._calculate_energy_distribution(alive_entities),
            "births_this_period": self._count_recent_births(world),
            "deaths_this_period": self._count_recent_deaths(world)
        }
        
        self.population_history.append(population_data)
    
    def _collect_stress_data(self, world):
        """Recopila datos de estrés y psicología."""
        if not hasattr(world, 'stress_psychology_system'):
            return
        
        report = world.stress_psychology_system.get_population_report()
        
        stress_data = {
            "timestamp": self.simulation_tick,
            "social_breakdown_percentage": report.get('social_breakdown_percentage', 0),
            "stress_distribution": report.get('stress_distribution', {}),
            "anomalous_behaviors": report.get('anomalous_behaviors', {}),
            "critical_indicators": report.get('critical_indicators', {}),
            "population_collapsing": world.stress_psychology_system.is_population_collapsing()
        }
        
        self.stress_history.append(stress_data)
    
    def _collect_behavior_data(self, world):
        """Recopila datos de comportamiento."""
        alive_entities = [e for e in world.entities if e.alive]
        
        # Contar estados
        state_counts = defaultdict(int)
        for entity in alive_entities:
            state_counts[entity.state] += 1
        
        # Analizar personalidades
        personality_distribution = defaultdict(int)
        for entity in alive_entities:
            if hasattr(entity, 'personality_type'):
                personality_distribution[entity.personality_type] += 1
        
        behavior_data = {
            "timestamp": self.simulation_tick,
            "state_distribution": dict(state_counts),
            "personality_distribution": dict(personality_distribution),
            "social_interactions": self._count_social_interactions(alive_entities),
            "territorial_conflicts": self._count_territorial_conflicts(world),
            "reproduction_events": self._count_reproduction_events(alive_entities)
        }
        
        self.behavior_history.append(behavior_data)
    
    def _collect_resource_data(self, world):
        """Recopila datos de recursos."""
        resource_data = {
            "timestamp": self.simulation_tick,
            "total_resources": len(world.resources),
            "resource_types": self._count_resource_types(world.resources),
            "resource_density": len(world.resources) / (world.config.WORLD_SIZE[0] * world.config.WORLD_SIZE[1] / 1000000),
            "consumption_rate": self._calculate_consumption_rate(world),
            "regeneration_rate": getattr(world, 'resource_regeneration_rate', 0)
        }
        
        self.resource_history.append(resource_data)
    
    def _collect_territory_data(self, world):
        """Recopila datos de territorio."""
        if not hasattr(world, 'territory_system'):
            return
        
        territories = world.territory_system.territories
        
        territory_data = {
            "timestamp": self.simulation_tick,
            "total_territories": len(territories),
            "average_territory_size": self._calculate_average_territory_size(territories),
            "territory_conflicts": len([t for t in territories.values() if t.active_conflicts]),
            "territorial_coverage": self._calculate_territorial_coverage(territories, world)
        }
        
        self.territory_history.append(territory_data)
    
    def _calculate_age_distribution(self, entities):
        """Calcula la distribución de edades."""
        if not entities:
            return {}
        
        ages = [e.age for e in entities]
        return {
            "min": min(ages),
            "max": max(ages),
            "mean": sum(ages) / len(ages),
            "median": sorted(ages)[len(ages) // 2]
        }
    
    def _calculate_health_distribution(self, entities):
        """Calcula la distribución de salud."""
        if not entities:
            return {}
        
        health_values = [e.health for e in entities]
        return {
            "min": min(health_values),
            "max": max(health_values),
            "mean": sum(health_values) / len(health_values),
            "below_50": len([h for h in health_values if h < 50])
        }
    
    def _calculate_energy_distribution(self, entities):
        """Calcula la distribución de energía."""
        if not entities:
            return {}
        
        energy_values = [e.energy for e in entities]
        return {
            "min": min(energy_values),
            "max": max(energy_values),
            "mean": sum(energy_values) / len(energy_values),
            "below_30": len([e for e in energy_values if e < 30])
        }
    
    def _count_recent_births(self, world):
        """Cuenta nacimientos recientes."""
        # Implementación simplificada
        return len([e for e in world.entities if e.alive and e.age < 10])
    
    def _count_recent_deaths(self, world):
        """Cuenta muertes recientes."""
        # Implementación simplificada
        return len([e for e in world.entities if not e.alive])
    
    def _count_social_interactions(self, entities):
        """Cuenta interacciones sociales."""
        interactions = 0
        for entity in entities:
            if entity.state in ["socializing", "teaching", "learning"]:
                interactions += 1
        return interactions
    
    def _count_territorial_conflicts(self, world):
        """Cuenta conflictos territoriales."""
        if not hasattr(world, 'territory_system'):
            return 0
        
        conflicts = 0
        for territory in world.territory_system.territories.values():
            conflicts += len(territory.active_conflicts)
        return conflicts
    
    def _count_reproduction_events(self, entities):
        """Cuenta eventos de reproducción."""
        return len([e for e in entities if e.state == "reproducing"])
    
    def _count_resource_types(self, resources):
        """Cuenta tipos de recursos."""
        type_counts = defaultdict(int)
        for resource in resources:
            if not resource.consumed:
                type_counts[resource.type] += 1
        return dict(type_counts)
    
    def _calculate_consumption_rate(self, world):
        """Calcula la tasa de consumo de recursos."""
        # Implementación simplificada
        consumed_resources = len([r for r in world.resources if r.consumed])
        total_resources = len(world.resources)
        return consumed_resources / max(1, total_resources)
    
    def _calculate_average_territory_size(self, territories):
        """Calcula el tamaño promedio de territorio."""
        if not territories:
            return 0
        
        sizes = [t.size for t in territories.values()]
        return sum(sizes) / len(sizes)
    
    def _calculate_territorial_coverage(self, territories, world):
        """Calcula la cobertura territorial."""
        if not territories:
            return 0
        
        total_area = world.config.WORLD_SIZE[0] * world.config.WORLD_SIZE[1]
        territorial_area = sum(math.pi * t.size**2 for t in territories.values())
        return min(1.0, territorial_area / total_area)
    
    def _update_current_metrics(self, world):
        """Actualiza métricas actuales."""
        self.current_metrics = {
            "simulation_time": self.simulation_tick,
            "real_time_elapsed": time.time() - self.session_start_time,
            "population": len([e for e in world.entities if e.alive]),
            "stress_level": self._get_average_stress(world),
            "resource_availability": len(world.resources),
            "active_territories": len(getattr(world.territory_system, 'territories', {})) if hasattr(world, 'territory_system') else 0
        }
    
    def _get_average_stress(self, world):
        """Obtiene el nivel promedio de estrés."""
        if not hasattr(world, 'stress_psychology_system'):
            return 0
        
        alive_entities = [e for e in world.entities if e.alive]
        if not alive_entities:
            return 0
        
        stress_levels = []
        for entity in alive_entities:
            if hasattr(entity, 'stress_level'):
                stress_levels.append(entity.stress_level)
        
        return sum(stress_levels) / len(stress_levels) if stress_levels else 0
    
    def _detect_significant_events(self, world):
        """Detecta eventos significativos."""
        current_population = len([e for e in world.entities if e.alive])
        
        # Detectar cambios drásticos de población
        if len(self.population_history) > 1:
            prev_population = self.population_history[-1]["total_population"]
            population_change = abs(current_population - prev_population)
            
            if population_change > prev_population * 0.1:  # Cambio del 10%
                event = {
                    "timestamp": self.simulation_tick,
                    "type": "population_change",
                    "description": f"Cambio drástico de población: {prev_population} -> {current_population}",
                    "severity": "high" if population_change > prev_population * 0.2 else "medium"
                }
                self.significant_events.append(event)
        
        # Detectar colapso social
        if hasattr(world, 'stress_psychology_system'):
            if world.stress_psychology_system.is_population_collapsing():
                event = {
                    "timestamp": self.simulation_tick,
                    "type": "social_collapse",
                    "description": "Colapso social detectado",
                    "severity": "critical"
                }
                self.significant_events.append(event)
    
    def _perform_statistical_analysis(self):
        """Realiza análisis estadístico de los datos."""
        if len(self.population_history) < 10:
            return
        
        # Análisis de tendencias de población
        recent_populations = [p["total_population"] for p in list(self.population_history)[-50:]]
        self.trend_analysis["population_trend"] = self._calculate_trend(recent_populations)
        
        # Análisis de correlación estrés-población
        if len(self.stress_history) >= 10:
            stress_levels = [s["social_breakdown_percentage"] for s in list(self.stress_history)[-50:]]
            populations = [p["total_population"] for p in list(self.population_history)[-50:]]
            
            if len(stress_levels) == len(populations):
                self.correlation_data["stress_population"] = self._calculate_correlation(stress_levels, populations)
    
    def _calculate_trend(self, data):
        """Calcula la tendencia de una serie de datos."""
        if len(data) < 2:
            return "stable"
        
        # Regresión lineal simple
        n = len(data)
        x = list(range(n))
        y = data
        
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i]**2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x**2)
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
    
    def _calculate_correlation(self, x, y):
        """Calcula la correlación entre dos series de datos."""
        if len(x) != len(y) or len(x) < 2:
            return 0
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i]**2 for i in range(n))
        sum_y2 = sum(y[i]**2 for i in range(n))
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x**2) * (n * sum_y2 - sum_y**2))
        
        return numerator / denominator if denominator != 0 else 0
    
    def _detect_anomalies(self):
        """Detecta anomalías en los datos."""
        # Detectar picos de población
        if len(self.population_history) > 20:
            populations = [p["total_population"] for p in list(self.population_history)[-20:]]
            mean_pop = sum(populations) / len(populations)
            
            for i, pop in enumerate(populations[-5:]):  # Últimos 5 datos
                if pop > mean_pop * 1.5:  # 50% por encima de la media
                    self.anomaly_detection["population_spikes"].append({
                        "timestamp": self.simulation_tick - (4 - i),
                        "value": pop,
                        "threshold": mean_pop * 1.5
                    })
    
    def export_data(self, format="json", filename=None):
        """Exporta los datos recopilados.
        
        Args:
            format (str): Formato de exportación ('json', 'csv').
            filename (str, optional): Nombre del archivo.
        
        Returns:
            str: Ruta del archivo exportado.
        """
        if not self.export_enabled:
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if filename is None:
            filename = f"universe25_data_{timestamp}"
        
        if format == "json":
            return self._export_json(filename)
        elif format == "csv":
            return self._export_csv(filename)
        
        return None
    
    def _export_json(self, filename):
        """Exporta datos en formato JSON."""
        data = {
            "metadata": {
                "export_timestamp": datetime.now().isoformat(),
                "simulation_ticks": self.simulation_tick,
                "session_duration": time.time() - self.session_start_time
            },
            "population_history": list(self.population_history),
            "stress_history": list(self.stress_history),
            "behavior_history": list(self.behavior_history),
            "resource_history": list(self.resource_history),
            "territory_history": list(self.territory_history),
            "significant_events": self.significant_events,
            "statistical_analysis": self.statistical_analysis,
            "anomaly_detection": self.anomaly_detection
        }
        
        filepath = f"{filename}.json"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def _export_csv(self, filename):
        """Exporta datos en formato CSV."""
        # Exportar datos de población
        pop_filepath = f"{filename}_population.csv"
        with open(pop_filepath, 'w', newline='', encoding='utf-8') as f:
            if self.population_history:
                writer = csv.DictWriter(f, fieldnames=self.population_history[0].keys())
                writer.writeheader()
                writer.writerows(self.population_history)
        
        return pop_filepath
    
    def get_summary_report(self):
        """Genera un reporte resumen de la simulación.
        
        Returns:
            dict: Reporte resumen.
        """
        return {
            "simulation_duration": self.simulation_tick,
            "current_metrics": self.current_metrics,
            "significant_events_count": len(self.significant_events),
            "data_points_collected": len(self.population_history),
            "trend_analysis": self.trend_analysis,
            "anomalies_detected": {
                key: len(value) for key, value in self.anomaly_detection.items()
            }
        }
