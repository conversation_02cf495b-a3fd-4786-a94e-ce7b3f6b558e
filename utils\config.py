#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo de configuración para el simulador Nexus.

Contiene todas las constantes y parámetros configurables del simulador.
"""

import pygame

class Config:
    """Clase que contiene la configuración del simulador."""

    def __init__(self):
        # Configuración de pantalla
        self.SCREEN_WIDTH = 1280
        self.SCREEN_HEIGHT = 720
        self.FPS = 60
        self.BACKGROUND_COLOR = (50, 120, 80)  # Verde bosque

        # Colores
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        self.BLUE = (0, 0, 255)
        self.YELLOW = (255, 255, 0)

        # Configuración del mundo
        self.WORLD_SIZE = (10000, 10000)  # Tamaño del mundo mucho más grande
        self.TILE_SIZE = 32
        self.MAX_RESOURCES = 500  # Aumentado para proporcionar más alimento en un mundo más grande
        self.RESOURCE_RESPAWN_RATE = 0.02  # Tasa de reaparición de recursos
        self.MAX_PREDATORS = 15  # Más depredadores para un mundo más grande
        self.WEATHER_CHANGE_RATE = 0.001  # Probabilidad de cambio climático por tick

        # Configuración de Nexus
        self.NEXUS_SIZE = 20
        self.NEXUS_SPEED = 2.5  # Velocidad base
        self.NEXUS_VISION_RANGE = 300  # Aumentado para mejor percepción
        self.NEXUS_MAX_ENERGY = 100
        self.NEXUS_ENERGY_CONSUMPTION_RATE = 0.02  # Reducido para mayor supervivencia
        self.NEXUS_REPRODUCTION_ENERGY_THRESHOLD = 60  # Energía mínima para reproducción
        self.NEXUS_REPRODUCTION_ENERGY_COST = 30  # Energía consumida en reproducción
        self.NEXUS_MAX_AGE = 30000  # En ticks (aumentado significativamente para mayor longevidad)
        self.NEXUS_MATURITY_AGE = 1500  # Edad de madurez

        # Configuración de genética
        self.MUTATION_RATE = 0.1  # Probabilidad de mutación por atributo
        self.MUTATION_AMOUNT = 0.2  # Cantidad máxima de cambio por mutación

        # Configuración de aprendizaje
        self.LEARNING_RATE = 0.01  # Velocidad de aprendizaje
        self.MEMORY_CAPACITY = 100  # Número máximo de recuerdos (aumentado)
        self.MEMORY_DECAY_RATE = 0.0005  # Tasa de olvido por tick (reducida para mejor memoria)

        # Configuración de relaciones sociales
        self.RELATIONSHIP_DECAY_RATE = 0.0002  # Tasa de deterioro de relaciones por tick (reducida)
        self.MAX_TRIBE_SIZE = 25  # Tamaño máximo de una tribu (aumentado)
        self.TRIBE_FORMATION_THRESHOLD = 0.6  # Afinidad mínima para formar una tribu (reducida)

        # Configuración de tecnología
        self.TECH_DISCOVERY_CHANCE = 0.0001  # Probabilidad de descubrir tecnología por tick
        self.TECH_SHARING_RADIUS = 100  # Radio en el que se comparte tecnología

        # Configuración de interfaz
        self.FONT_SIZE = 16
        self.UI_PADDING = 10
        self.UI_BACKGROUND_COLOR = (30, 30, 30, 200)  # RGBA con transparencia
        self.UI_TEXT_COLOR = self.WHITE
        self.UI_BORDER_COLOR = self.WHITE
        self.UI_BORDER_WIDTH = 1

        # Configuración de sistemas avanzados (Universe 25)
        self.STRESS_SYSTEM_ENABLED = True
        self.TERRITORY_SYSTEM_ENABLED = True
        self.RESOURCE_MANAGEMENT_ENABLED = True
        self.AUTONOMOUS_AI_ENABLED = True

        # Configuración de estrés poblacional
        self.POPULATION_STRESS_THRESHOLD = 50  # Población donde comienza el estrés
        self.OVERCROWDING_RADIUS = 100         # Radio para detectar hacinamiento
        self.STRESS_DECAY_RATE = 0.001         # Tasa de reducción natural del estrés

        # Configuración de territorio
        self.TERRITORY_MIN_SIZE = 50           # Tamaño mínimo de territorio
        self.TERRITORY_MAX_SIZE = 300          # Tamaño máximo de territorio
        self.TERRITORY_ESTABLISHMENT_ENERGY = 40  # Energía mínima para establecer territorio

        # Configuración de recursos avanzados
        self.RESOURCE_DECAY_ENABLED = True     # Habilitar degradación de recursos
        self.TOOL_CRAFTING_ENABLED = True      # Habilitar fabricación de herramientas
        self.INVENTORY_CAPACITY = 10           # Capacidad base de inventario

        # Configuración de comportamientos emergentes
        self.ANOMALOUS_BEHAVIOR_CHANCE = 0.1   # Probabilidad de desarrollar comportamientos anómalos
        self.SOCIAL_BREAKDOWN_THRESHOLD = 0.6  # Umbral de colapso social
        self.BEAUTIFUL_ONES_CHANCE = 0.3       # Probabilidad de comportamiento "beautiful ones"

        # Inicializar fuentes
        pygame.font.init()
        self.FONT = pygame.font.SysFont("Arial", self.FONT_SIZE)