#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo de inteligencia artificial autónoma para el simulador Nexus.

Implementa un sistema de toma de decisiones más avanzado para los Nexus.
"""

import random
import math
import numpy as np

class AutonomousAI:
    """Sistema de inteligencia artificial autónoma avanzado para los Nexus con comportamientos emergentes."""

    def __init__(self, config):
        """Inicializa el sistema de IA autónoma.

        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config

        # Pesos de factores para toma de decisiones (más complejos)
        self.decision_weights = {
            "survival": 1.0,      # Supervivencia (prioridad máxima)
            "energy": 0.8,        # Necesidad de energía
            "safety": 0.7,        # Seguridad
            "social": 0.5,        # Necesidades sociales
            "curiosity": 0.4,     # Exploración y aprendizaje
            "reproduction": 0.6,  # Reproducción
            "tribe": 0.5,         # Lealtad a la tribu
            "personal": 0.3,      # Objetivos personales
            "territory": 0.6,     # Control territorial
            "status": 0.4,        # Estatus social
            "stress": -0.8,       # Estrés (factor negativo)
            "comfort": 0.3,       # Búsqueda de comodidad
            "dominance": 0.5,     # Comportamiento dominante
            "avoidance": 0.4      # Evitación de conflictos
        }

        # Tipos de personalidad que afectan el comportamiento
        self.personality_archetypes = {
            "alpha": {"dominance": 0.9, "aggression": 0.8, "leadership": 0.9, "territory": 0.8},
            "beta": {"dominance": 0.6, "aggression": 0.4, "leadership": 0.6, "territory": 0.5},
            "omega": {"dominance": 0.2, "aggression": 0.2, "leadership": 0.1, "territory": 0.2},
            "explorer": {"curiosity": 0.9, "independence": 0.8, "territory": 0.3, "social": 0.4},
            "caretaker": {"empathy": 0.9, "social": 0.8, "aggression": 0.2, "nurturing": 0.9},
            "hermit": {"social": 0.2, "independence": 0.9, "territory": 0.7, "avoidance": 0.8}
        }
        
        # Estados posibles (expandidos para comportamientos complejos)
        self.states = [
            "idle",             # En reposo
            "seeking_food",     # Buscando comida
            "seeking_water",    # Buscando agua
            "seeking_mate",     # Buscando pareja
            "exploring",        # Explorando
            "fleeing",          # Huyendo
            "resting",          # Descansando
            "socializing",      # Socializando
            "building",         # Construyendo
            "hunting",          # Cazando
            "gathering",        # Recolectando
            "teaching",         # Enseñando
            "learning",         # Aprendiendo
            "defending",        # Defendiendo
            "attacking",        # Atacando
            "migrating",        # Migrando
            "thinking",         # Pensando (para desarrollo de ideas)
            "establishing_territory", # Estableciendo territorio
            "competing",        # Compitiendo por recursos
            "grooming",         # Acicalándose (social)
            "patrolling",       # Patrullando territorio
            "withdrawing",      # Retirándose socialmente
            "aggressive",       # Comportamiento agresivo
            "submissive",       # Comportamiento sumiso
            "stressed",         # Estado de estrés
            "caring",           # Cuidando a otros
            "hoarding",         # Acumulando recursos
            "seeking_shelter"   # Buscando refugio
        ]
        
        # Memoria de decisiones para evitar cambios constantes
        self.decision_memory = {}  # {nexus_id: {"last_decision": state, "duration": ticks}}

        # Factores de estrés poblacional (Universe 25)
        self.population_stress_factors = {
            "overcrowding": 0.0,      # Factor de hacinamiento
            "resource_scarcity": 0.0, # Escasez de recursos
            "social_pressure": 0.0,   # Presión social
            "territory_conflict": 0.0 # Conflictos territoriales
        }
        
        # Factores de personalidad que afectan decisiones
        self.personality_factors = {
            "aggression": {
                "attacking": 0.8,
                "defending": 0.5,
                "fleeing": -0.7
            },
            "sociability": {
                "socializing": 0.8,
                "teaching": 0.6,
                "exploring": -0.3
            },
            "curiosity": {
                "exploring": 0.8,
                "learning": 0.7,
                "idle": -0.5
            },
            "selfishness": {
                "teaching": -0.7,
                "sharing": -0.8,
                "gathering": 0.4
            }
        }
    
    def update(self, nexus, world):
        """Actualiza la IA del Nexus, tomando decisiones autónomas.
        
        Args:
            nexus (Nexus): Nexus a actualizar.
            world (World): Referencia al mundo de la simulación.
        """
        if not nexus.alive:
            return
        
        # Obtener ID único para el Nexus
        nexus_id = id(nexus)
        
        # Verificar si debe mantener la decisión actual
        if nexus_id in self.decision_memory:
            memory = self.decision_memory[nexus_id]
            memory["duration"] -= 1
            
            # Mantener decisión actual si no ha expirado
            if memory["duration"] > 0:
                return
        
        # Evaluar situación actual
        situation = self._evaluate_situation(nexus, world)
        
        # Tomar decisión basada en la situación
        new_state, target = self._make_decision(nexus, situation, world)
        
        # Actualizar estado y objetivo del Nexus
        if new_state != nexus.state:
            nexus.state = new_state
            nexus.target = target
            
            # Guardar en memoria de decisiones con duración aleatoria
            # (más inteligente = decisiones más estables)
            min_duration = int(20 * nexus.attributes["intelligence"])
            max_duration = int(60 * nexus.attributes["intelligence"])
            duration = random.randint(min_duration, max_duration)
            
            self.decision_memory[nexus_id] = {
                "last_decision": new_state,
                "duration": duration
            }
    
    def _evaluate_situation(self, nexus, world):
        """Evalúa la situación actual del Nexus.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            world (World): Referencia al mundo.
            
        Returns:
            dict: Evaluación de la situación actual.
        """
        situation = {
            "energy": nexus.energy / 100.0,  # Normalizado a 0-1
            "health": nexus.health / 100.0,
            "age": min(1.0, nexus.age / nexus.max_age),
            "danger": 0.0,
            "social_need": 0.0,
            "reproduction_need": 0.0,
            "curiosity_need": 0.0,
            "tribe_need": 0.0,
            "nearby_resources": [],
            "nearby_nexus": [],
            "nearby_predators": [],
            "nearby_shelters": [],
            "weather_condition": world.weather,
            "time_of_day": getattr(world, "time_of_day", "day")
        }
        
        # Evaluar peligro (depredadores cercanos)
        try:
            predators = world.get_nearby_predators(nexus.position, 200)
            situation["nearby_predators"] = predators
            situation["danger"] = min(1.0, len(predators) * 0.3)
        except:
            situation["nearby_predators"] = []
            situation["danger"] = 0.0

        # Evaluar necesidad social
        try:
            nearby_nexus = world.get_nearby_nexus(nexus.position, 200, exclude=nexus)
            situation["nearby_nexus"] = nearby_nexus
        except:
            situation["nearby_nexus"] = []
        
        # Necesidad social basada en sociabilidad y tiempo desde última interacción
        last_social = 0
        for memory in nexus.memory:
            if "socializó" in memory["text"].lower() or "comunicó" in memory["text"].lower():
                last_social = memory["time"]
                break
        
        time_since_social = nexus.age - last_social
        social_isolation = min(1.0, time_since_social / 1000)  # Normalizado
        situation["social_need"] = social_isolation * nexus.personality["sociability"]
        
        # Necesidad de reproducción
        if nexus.age > nexus.sexual_maturity_age:
            time_since_reproduction = nexus.age - nexus.last_reproduction_time
            reproduction_need = min(1.0, time_since_reproduction / 2000)  # Normalizado
            situation["reproduction_need"] = reproduction_need
        
        # Necesidad de exploración/curiosidad
        explored_area = getattr(nexus, "explored_area", set())
        curiosity_need = nexus.attributes["curiosity"] * (1.0 - min(1.0, len(explored_area) / 1000))
        situation["curiosity_need"] = curiosity_need
        
        # Necesidad tribal (lealtad a la tribu)
        if nexus.tribe:
            # Distancia a territorio tribal
            if hasattr(nexus.tribe, "territory_center"):
                distance_to_tribe = math.sqrt(
                    (nexus.position[0] - nexus.tribe.territory_center[0])**2 +
                    (nexus.position[1] - nexus.tribe.territory_center[1])**2
                )
                # Normalizar por radio del territorio
                territory_radius = getattr(nexus.tribe, "territory_radius", 200)
                distance_factor = min(1.0, distance_to_tribe / (territory_radius * 2))
                situation["tribe_need"] = distance_factor * (1.0 - nexus.attributes["selfishness"])
        
        # Recursos cercanos
        try:
            situation["nearby_resources"] = world.get_nearby_resources(nexus.position, 300)
        except:
            situation["nearby_resources"] = []
        
        # Refugios cercanos
        if hasattr(world, "shelter_system"):
            # Get nearby shelters using the correct method
            try:
                if hasattr(world.shelter_system, 'get_nearby_shelters'):
                    situation["nearby_shelters"] = world.shelter_system.get_nearby_shelters(nexus.position, 300)
                elif hasattr(world.shelter_system, 'get_nearest_shelter'):
                    nearest_shelter = world.shelter_system.get_nearest_shelter(nexus.position)
                    situation["nearby_shelters"] = [nearest_shelter] if nearest_shelter else []
                else:
                    situation["nearby_shelters"] = []
            except:
                situation["nearby_shelters"] = []
        else:
            situation["nearby_shelters"] = []
        
        return situation
    
    def _make_decision(self, nexus, situation, world):
        """Toma una decisión basada en la situación actual.
        
        Args:
            nexus (Nexus): Nexus que toma la decisión.
            situation (dict): Evaluación de la situación actual.
            world (World): Referencia al mundo.
            
        Returns:
            tuple: (nuevo_estado, objetivo)
        """
        # Prioridad 1: Supervivencia crítica
        if situation["health"] < 0.2 or situation["danger"] > 0.7:
            # Huir de depredadores o buscar refugio
            if situation["nearby_predators"]:
                # Dirección opuesta al depredador más cercano
                predator = situation["nearby_predators"][0]
                flee_x = nexus.position[0] + (nexus.position[0] - predator.position[0]) * 2
                flee_y = nexus.position[1] + (nexus.position[1] - predator.position[1]) * 2
                return "fleeing", [flee_x, flee_y]
            
            # Buscar refugio si hay alguno cerca
            if situation["nearby_shelters"]:
                shelter = situation["nearby_shelters"][0]
                return "seeking_shelter", shelter.position
        
        # Prioridad 2: Necesidades básicas (energía)
        if situation["energy"] < 0.3:
            # Buscar comida
            food_resources = [r for r in situation["nearby_resources"] if r.type == "food"]
            if food_resources:
                return "seeking_food", food_resources[0].position
            
            # Si tiene conocimiento de caza y hay otros Nexus cerca, intentar caza en grupo
            if "Cazar en grupo" in nexus.knowledge and len(situation["nearby_nexus"]) >= 2:
                return "hunting", self._find_hunting_target(nexus, world)
            
            # Si tiene conocimiento de recolección, buscar plantas
            if "Recolectar bayas" in nexus.knowledge:
                plants = [p for p in getattr(world, "plants", []) if hasattr(p, "position")]
                if plants:
                    nearest_plant = min(plants, key=lambda p: 
                                      math.sqrt((p.position[0] - nexus.position[0])**2 + 
                                              (p.position[1] - nexus.position[1])**2))
                    return "gathering", nearest_plant.position
            
            # Explorar en busca de comida
            return "seeking_food", self._generate_exploration_target(nexus, world)
        
        # Prioridad 3: Descanso si está agotado
        if situation["energy"] < 0.2:
            # Buscar lugar seguro para descansar
            if situation["nearby_shelters"]:
                shelter = situation["nearby_shelters"][0]
                return "resting", shelter.position
            else:
                # Descansar donde está
                return "resting", None
        
        # Calcular puntuaciones para cada posible estado
        state_scores = self._calculate_state_scores(nexus, situation)
        
        # Seleccionar el estado con mayor puntuación
        best_state = max(state_scores.items(), key=lambda x: x[1])[0]
        
        # Determinar objetivo según el estado seleccionado
        target = self._determine_target(nexus, best_state, situation, world)
        
        return best_state, target
    
    def _calculate_state_scores(self, nexus, situation):
        """Calcula puntuaciones para cada posible estado.
        
        Args:
            nexus (Nexus): Nexus que toma la decisión.
            situation (dict): Evaluación de la situación actual.
            
        Returns:
            dict: Puntuaciones para cada estado.
        """
        scores = {state: 0.0 for state in self.states}
        
        # Factores base según la situación
        scores["seeking_food"] = (1.0 - situation["energy"]) * self.decision_weights["energy"]
        scores["resting"] = (1.0 - situation["energy"]) * 0.5
        scores["fleeing"] = situation["danger"] * self.decision_weights["safety"]
        scores["socializing"] = situation["social_need"] * self.decision_weights["social"]
        scores["seeking_mate"] = situation["reproduction_need"] * self.decision_weights["reproduction"]
        scores["exploring"] = situation["curiosity_need"] * self.decision_weights["curiosity"]
        
        # Ajustar por personalidad
        for trait, effects in self.personality_factors.items():
            if trait in nexus.personality:
                trait_value = nexus.personality[trait]
                for state, effect in effects.items():
                    if state in scores:
                        scores[state] += trait_value * effect * 0.3
        
        # Ajustar por conocimientos
        if "Hacer fuego" in nexus.knowledge:
            scores["resting"] += 0.2  # Mejor descanso con fuego
        
        if "Fabricar lanza" in nexus.knowledge:
            scores["hunting"] += 0.3  # Mejor caza con lanza
        
        if "Construir refugio" in nexus.knowledge:
            scores["building"] += 0.4  # Puede construir
        
        # Ajustar por emociones
        if "fear" in nexus.emotions and nexus.emotions["fear"] > 0.6:
            scores["fleeing"] += nexus.emotions["fear"] * 0.5
            scores["exploring"] -= nexus.emotions["fear"] * 0.3
        
        if "happiness" in nexus.emotions and nexus.emotions["happiness"] > 0.7:
            scores["socializing"] += nexus.emotions["happiness"] * 0.3
            scores["teaching"] += nexus.emotions["happiness"] * 0.2
        
        # Ajustar por tiempo y clima
        if situation["time_of_day"] == "night":
            scores["resting"] += 0.3
            scores["exploring"] -= 0.3
        
        if situation["weather_condition"] in ["stormy", "snowy"]:
            scores["seeking_shelter"] += 0.4
            scores["exploring"] -= 0.4
        
        # Añadir algo de aleatoriedad para comportamiento más natural
        for state in scores:
            scores[state] += random.uniform(-0.1, 0.1)
        
        return scores
    
    def _determine_target(self, nexus, state, situation, world):
        """Determina el objetivo para un estado dado.
        
        Args:
            nexus (Nexus): Nexus que toma la decisión.
            state (str): Estado seleccionado.
            situation (dict): Evaluación de la situación.
            world (World): Referencia al mundo.
            
        Returns:
            object: Objetivo (posición, entidad, etc.)
        """
        if state == "seeking_food":
            food_resources = [r for r in situation["nearby_resources"] if r.type == "food"]
            if food_resources:
                return food_resources[0].position
            return self._generate_exploration_target(nexus, world)
        
        elif state == "seeking_water":
            water_resources = [r for r in situation["nearby_resources"] if r.type == "water"]
            if water_resources:
                return water_resources[0].position
            return self._generate_exploration_target(nexus, world)
        
        elif state == "seeking_mate":
            # Buscar pareja compatible
            potential_mates = [
                n for n in situation["nearby_nexus"]
                if n.alive and n.age > n.sexual_maturity_age and n.sex != nexus.sex
            ]
            if potential_mates:
                # Seleccionar por compatibilidad
                best_mate = max(potential_mates, key=lambda m: self._calculate_compatibility(nexus, m))
                return best_mate.position
            return self._generate_exploration_target(nexus, world)
        
        elif state == "exploring":
            return self._generate_exploration_target(nexus, world)
        
        elif state == "fleeing":
            if situation["nearby_predators"]:
                predator = situation["nearby_predators"][0]
                flee_x = nexus.position[0] + (nexus.position[0] - predator.position[0]) * 2
                flee_y = nexus.position[1] + (nexus.position[1] - predator.position[1]) * 2
                return [flee_x, flee_y]
            return None
        
        elif state == "socializing":
            if situation["nearby_nexus"]:
                # Preferir Nexus con buena relación
                best_social = max(situation["nearby_nexus"], 
                                key=lambda n: nexus.relationships.get(n.name, 0.5))
                return best_social.position
            return None
        
        elif state == "building":
            # Construir cerca del territorio tribal o en lugar seguro
            if nexus.tribe and hasattr(nexus.tribe, "territory_center"):
                build_x = nexus.tribe.territory_center[0] + random.uniform(-50, 50)
                build_y = nexus.tribe.territory_center[1] + random.uniform(-50, 50)
                return [build_x, build_y]
            return None
        
        elif state == "hunting":
            return self._find_hunting_target(nexus, world)
        
        elif state == "gathering":
            plants = [p for p in getattr(world, "plants", []) if hasattr(p, "position")]
            if plants:
                nearest_plant = min(plants, key=lambda p: 
                                  math.sqrt((p.position[0] - nexus.position[0])**2 + 
                                          (p.position[1] - nexus.position[1])**2))
                return nearest_plant.position
            return self._generate_exploration_target(nexus, world)
        
        elif state == "teaching" or state == "learning":
            if situation["nearby_nexus"]:
                if state == "teaching":
                    # Enseñar a Nexus con menos conocimiento
                    student = min(situation["nearby_nexus"], 
                                key=lambda n: len(getattr(n, "knowledge", [])))
                    return student.position
                else:
                    # Aprender de Nexus con más conocimiento
                    teacher = max(situation["nearby_nexus"], 
                                key=lambda n: len(getattr(n, "knowledge", [])))
                    return teacher.position
            return None
        
        elif state == "defending" or state == "attacking":
            if state == "defending" and nexus.tribe:
                # Defender territorio tribal
                return nexus.tribe.territory_center
            elif situation["nearby_predators"]:
                # Atacar depredador
                return situation["nearby_predators"][0].position
            return None
        
        elif state == "migrating":
            # Migrar a un área nueva
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(500, 1000)
            target_x = nexus.position[0] + math.cos(angle) * distance
            target_y = nexus.position[1] + math.sin(angle) * distance
            return [target_x, target_y]
        
        # Para estados como "idle", "resting", "thinking"
        return None
    
    def _generate_exploration_target(self, nexus, world):
        """Genera un objetivo de exploración inteligente.
        
        Args:
            nexus (Nexus): Nexus que explora.
            world (World): Referencia al mundo.
            
        Returns:
            list: Posición objetivo [x, y].
        """
        # Inicializar área explorada si no existe
        if not hasattr(nexus, "explored_area"):
            nexus.explored_area = set()
            
        # Dividir el mundo en sectores
        sector_size = 100
        current_sector = (
            int(nexus.position[0] / sector_size),
            int(nexus.position[1] / sector_size)
        )
        
        # Añadir sector actual a explorados
        nexus.explored_area.add(current_sector)
        
        # Generar sectores adyacentes
        adjacent_sectors = []
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx == 0 and dy == 0:
                    continue
                    
                adj_sector = (current_sector[0] + dx, current_sector[1] + dy)
                adjacent_sectors.append(adj_sector)
        
        # Filtrar sectores no explorados
        unexplored = [s for s in adjacent_sectors if s not in nexus.explored_area]
        
        if unexplored:
            # Elegir sector no explorado
            target_sector = random.choice(unexplored)
            
            # Convertir a coordenadas
            target_x = (target_sector[0] + random.random()) * sector_size
            target_y = (target_sector[1] + random.random()) * sector_size
            
            # Verificar que esté dentro del mundo
            if hasattr(world, "is_in_world"):
                if not world.is_in_world((target_x, target_y)):
                    # Si está fuera, explorar hacia el centro
                    angle = random.uniform(0, 2 * math.pi)
                    distance = random.uniform(100, 200)
                    target_x = nexus.position[0] + math.cos(angle) * distance
                    target_y = nexus.position[1] + math.sin(angle) * distance
        else:
            # Si todos los sectores adyacentes están explorados, ir más lejos
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(200, 400)
            target_x = nexus.position[0] + math.cos(angle) * distance
            target_y = nexus.position[1] + math.sin(angle) * distance
        
        return [target_x, target_y]
    
    def _find_hunting_target(self, nexus, world):
        """Encuentra un objetivo para cazar.
        
        Args:
            nexus (Nexus): Nexus cazador.
            world (World): Referencia al mundo.
            
        Returns:
            list: Posición objetivo [x, y].
        """
        # Buscar animales cercanos
        animals = getattr(world, "animals", [])
        nearby_animals = []
        
        for animal in animals:
            if hasattr(animal, "position") and hasattr(animal, "size"):
                distance = math.sqrt(
                    (nexus.position[0] - animal.position[0])**2 +
                    (nexus.position[1] - animal.position[1])**2
                )
                
                if distance < 500:  # Radio de detección
                    nearby_animals.append((animal, distance))
        
        if nearby_animals:
            # Ordenar por tamaño y distancia (preferir animales pequeños y cercanos)
            nearby_animals.sort(key=lambda a: a[0].size * a[1])
            
            # Seleccionar objetivo
            target_animal = nearby_animals[0][0]
            return target_animal.position
        
        # Si no hay animales, explorar
        return self._generate_exploration_target(nexus, world)
    
    def _calculate_compatibility(self, nexus1, nexus2):
        """Calcula la compatibilidad entre dos Nexus para reproducción.
        
        Args:
            nexus1 (Nexus): Primer Nexus.
            nexus2 (Nexus): Segundo Nexus.
            
        Returns:
            float: Valor de compatibilidad (0-1).
        """
        # Verificar sexos opuestos (si es reproducción sexual)
        if hasattr(nexus1, "reproduction_mode") and nexus1.reproduction_mode == "sexual":
            if nexus1.sex == nexus2.sex:
                return 0.0
        
        # Calcular similitud de atributos (preferir cierta diversidad)
        attribute_similarity = 0.0
        attribute_count = 0
        
        for attr in nexus1.attributes:
            if attr in nexus2.attributes:
                # Diferencia moderada es óptima (ni muy similar ni muy diferente)
                diff = abs(nexus1.attributes[attr] - nexus2.attributes[attr])
                # Función que favorece diferencias moderadas (pico en ~0.3)
                similarity = 1.0 - abs(diff - 0.3) * 2
                attribute_similarity += max(0, similarity)
                attribute_count += 1
        
        if attribute_count > 0:
            attribute_similarity /= attribute_count
        
        # Considerar relación existente
        relationship = nexus1.relationships.get(nexus2.name, 0.5)
        
        # Evitar endogamia (parentesco cercano)
        inbreeding_penalty = 0.0
        if (hasattr(nexus1, "parent1") and hasattr(nexus2, "parent1")):
            if (nexus1.parent1 == nexus2 or nexus1.parent2 == nexus2 or
                nexus2.parent1 == nexus1 or nexus2.parent2 == nexus1):
                inbreeding_penalty = 0.8  # Fuerte penalización por parentesco directo
            elif (nexus1.parent1 == nexus2.parent1 or nexus1.parent1 == nexus2.parent2 or
                 nexus1.parent2 == nexus2.parent1 or nexus1.parent2 == nexus2.parent2):
                inbreeding_penalty = 0.5  # Penalización por hermanos
        
        # Calcular compatibilidad final
        compatibility = (
            attribute_similarity * 0.4 +
            relationship * 0.4 +
            random.random() * 0.2  # Componente aleatorio
        ) * (1.0 - inbreeding_penalty)
        
        return max(0.0, min(1.0, compatibility))
    
    def get_decision_stats(self):
        """Obtiene estadísticas sobre las decisiones tomadas.
        
        Returns:
            dict: Estadísticas de decisiones.
        """
        stats = {state: 0 for state in self.states}
        
        # Contar decisiones actuales
        for memory in self.decision_memory.values():
            state = memory["last_decision"]
            if state in stats:
                stats[state] += 1
        
        return stats
