#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de partículas avanzado para efectos visuales.

Proporciona efectos de partículas para diferentes eventos y estados
en la simulación Universe 25.
"""

import pygame
import math
import random
from collections import defaultdict

class ParticleSystem:
    """Sistema de partículas para efectos visuales avanzados."""
    
    def __init__(self, config):
        """Inicializa el sistema de partículas.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        self.particles = []
        self.emitters = {}
        
        # Tipos de partículas predefinidos
        self.particle_types = {
            "stress": {
                "color": (255, 100, 0),
                "size": 2,
                "life": 60,
                "gravity": -0.1,
                "fade": True
            },
            "happiness": {
                "color": (255, 255, 100),
                "size": 3,
                "life": 80,
                "gravity": -0.05,
                "fade": True
            },
            "aggression": {
                "color": (255, 50, 50),
                "size": 4,
                "life": 40,
                "gravity": 0.0,
                "fade": True
            },
            "social": {
                "color": (150, 255, 150),
                "size": 2,
                "life": 100,
                "gravity": -0.02,
                "fade": True
            },
            "territory": {
                "color": (200, 150, 255),
                "size": 3,
                "life": 120,
                "gravity": 0.0,
                "fade": True
            },
            "death": {
                "color": (100, 100, 100),
                "size": 5,
                "life": 200,
                "gravity": 0.05,
                "fade": True
            },
            "birth": {
                "color": (255, 200, 255),
                "size": 4,
                "life": 150,
                "gravity": -0.08,
                "fade": True
            },
            "food": {
                "color": (255, 200, 100),
                "size": 2,
                "life": 30,
                "gravity": 0.0,
                "fade": True
            },
            "water": {
                "color": (100, 200, 255),
                "size": 2,
                "life": 40,
                "gravity": 0.02,
                "fade": True
            },
            "conflict": {
                "color": (255, 0, 0),
                "size": 6,
                "life": 60,
                "gravity": 0.0,
                "fade": True
            }
        }
    
    def create_particle(self, x, y, particle_type, velocity=None, custom_props=None):
        """Crea una nueva partícula.
        
        Args:
            x (float): Posición X inicial.
            y (float): Posición Y inicial.
            particle_type (str): Tipo de partícula.
            velocity (tuple, optional): Velocidad inicial (vx, vy).
            custom_props (dict, optional): Propiedades personalizadas.
        """
        if particle_type not in self.particle_types:
            return
        
        # Obtener propiedades del tipo
        props = self.particle_types[particle_type].copy()
        
        # Aplicar propiedades personalizadas
        if custom_props:
            props.update(custom_props)
        
        # Velocidad aleatoria si no se especifica
        if velocity is None:
            velocity = (
                random.uniform(-2, 2),
                random.uniform(-2, 2)
            )
        
        particle = {
            "x": x,
            "y": y,
            "vx": velocity[0],
            "vy": velocity[1],
            "color": props["color"],
            "size": props["size"],
            "life": props["life"],
            "max_life": props["life"],
            "gravity": props["gravity"],
            "fade": props["fade"],
            "type": particle_type
        }
        
        self.particles.append(particle)
    
    def create_burst(self, x, y, particle_type, count=10, spread=2.0):
        """Crea una explosión de partículas.
        
        Args:
            x (float): Posición X del centro.
            y (float): Posición Y del centro.
            particle_type (str): Tipo de partícula.
            count (int): Número de partículas.
            spread (float): Dispersión de las velocidades.
        """
        for _ in range(count):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(0.5, spread)
            velocity = (
                math.cos(angle) * speed,
                math.sin(angle) * speed
            )
            
            # Pequeña variación en la posición inicial
            offset_x = random.uniform(-5, 5)
            offset_y = random.uniform(-5, 5)
            
            self.create_particle(x + offset_x, y + offset_y, particle_type, velocity)
    
    def create_emitter(self, emitter_id, x, y, particle_type, rate=1, duration=None):
        """Crea un emisor continuo de partículas.
        
        Args:
            emitter_id (str): ID único del emisor.
            x (float): Posición X del emisor.
            y (float): Posición Y del emisor.
            particle_type (str): Tipo de partícula a emitir.
            rate (int): Partículas por frame.
            duration (int, optional): Duración en frames (None = infinito).
        """
        self.emitters[emitter_id] = {
            "x": x,
            "y": y,
            "particle_type": particle_type,
            "rate": rate,
            "duration": duration,
            "timer": 0
        }
    
    def remove_emitter(self, emitter_id):
        """Elimina un emisor de partículas.
        
        Args:
            emitter_id (str): ID del emisor a eliminar.
        """
        if emitter_id in self.emitters:
            del self.emitters[emitter_id]
    
    def update(self):
        """Actualiza todas las partículas y emisores."""
        # Actualizar emisores
        for emitter_id, emitter in list(self.emitters.items()):
            emitter["timer"] += 1
            
            # Emitir partículas según la tasa
            if emitter["timer"] % max(1, 60 // emitter["rate"]) == 0:
                self.create_particle(
                    emitter["x"] + random.uniform(-10, 10),
                    emitter["y"] + random.uniform(-10, 10),
                    emitter["particle_type"]
                )
            
            # Eliminar emisor si ha expirado
            if emitter["duration"] and emitter["timer"] >= emitter["duration"]:
                del self.emitters[emitter_id]
        
        # Actualizar partículas
        for particle in list(self.particles):
            # Actualizar posición
            particle["x"] += particle["vx"]
            particle["y"] += particle["vy"]
            
            # Aplicar gravedad
            particle["vy"] += particle["gravity"]
            
            # Reducir vida
            particle["life"] -= 1
            
            # Eliminar partículas muertas
            if particle["life"] <= 0:
                self.particles.remove(particle)
    
    def render(self, screen, camera_offset, camera_zoom):
        """Renderiza todas las partículas.
        
        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (list): Offset de la cámara [x, y].
            camera_zoom (float): Zoom de la cámara.
        """
        for particle in self.particles:
            # Calcular posición en pantalla
            screen_x = (particle["x"] - camera_offset[0]) * camera_zoom
            screen_y = (particle["y"] - camera_offset[1]) * camera_zoom
            
            # Verificar si está en pantalla
            if (screen_x < -10 or screen_x > screen.get_width() + 10 or
                screen_y < -10 or screen_y > screen.get_height() + 10):
                continue
            
            # Calcular alpha si fade está activado
            alpha = 255
            if particle["fade"]:
                life_ratio = particle["life"] / particle["max_life"]
                alpha = int(255 * life_ratio)
            
            # Calcular tamaño escalado
            size = max(1, int(particle["size"] * camera_zoom))
            
            # Crear superficie temporal para alpha
            if alpha < 255:
                temp_surface = pygame.Surface((size * 2, size * 2), pygame.SRCALPHA)
                color_with_alpha = (*particle["color"], alpha)
                pygame.draw.circle(temp_surface, color_with_alpha, (size, size), size)
                screen.blit(temp_surface, (screen_x - size, screen_y - size))
            else:
                pygame.draw.circle(screen, particle["color"], 
                                 (int(screen_x), int(screen_y)), size)
    
    def create_stress_effect(self, nexus):
        """Crea efecto de partículas para estrés.
        
        Args:
            nexus (Nexus): Nexus estresado.
        """
        if hasattr(nexus, 'stress_level') and nexus.stress_level > 0.5:
            intensity = int(nexus.stress_level * 5)
            self.create_burst(nexus.position[0], nexus.position[1] - 20, 
                            "stress", intensity, 1.5)
    
    def create_social_effect(self, nexus1, nexus2, interaction_type):
        """Crea efecto de partículas para interacciones sociales.
        
        Args:
            nexus1 (Nexus): Primer Nexus.
            nexus2 (Nexus): Segundo Nexus.
            interaction_type (str): Tipo de interacción.
        """
        # Posición entre los dos Nexus
        mid_x = (nexus1.position[0] + nexus2.position[0]) / 2
        mid_y = (nexus1.position[1] + nexus2.position[1]) / 2
        
        if interaction_type == "positive":
            self.create_burst(mid_x, mid_y, "social", 8, 1.0)
        elif interaction_type == "conflict":
            self.create_burst(mid_x, mid_y, "conflict", 12, 2.0)
        elif interaction_type == "reproduction":
            self.create_burst(mid_x, mid_y, "birth", 15, 1.5)
    
    def create_territory_effect(self, nexus):
        """Crea efecto de partículas para establecimiento de territorio.
        
        Args:
            nexus (Nexus): Nexus que establece territorio.
        """
        if hasattr(nexus, 'territory_center') and hasattr(nexus, 'territory_size'):
            # Crear partículas en el perímetro del territorio
            center_x, center_y = nexus.territory_center
            radius = nexus.territory_size
            
            for i in range(20):
                angle = (i / 20) * 2 * math.pi
                x = center_x + math.cos(angle) * radius
                y = center_y + math.sin(angle) * radius
                
                self.create_particle(x, y, "territory", 
                                   velocity=(0, 0),
                                   custom_props={"life": 180})
    
    def create_death_effect(self, nexus):
        """Crea efecto de partículas para muerte.
        
        Args:
            nexus (Nexus): Nexus que muere.
        """
        self.create_burst(nexus.position[0], nexus.position[1], 
                        "death", 20, 3.0)
    
    def create_birth_effect(self, nexus):
        """Crea efecto de partículas para nacimiento.
        
        Args:
            nexus (Nexus): Nexus recién nacido.
        """
        self.create_burst(nexus.position[0], nexus.position[1], 
                        "birth", 25, 2.0)
    
    def create_resource_effect(self, resource):
        """Crea efecto de partículas para recursos.
        
        Args:
            resource (Resource): Recurso consumido.
        """
        particle_type = "food" if resource.type == "food" else "water"
        self.create_burst(resource.position[0], resource.position[1], 
                        particle_type, 8, 1.0)
    
    def create_environmental_effects(self, world):
        """Crea efectos ambientales basados en el clima.
        
        Args:
            world (World): Mundo de la simulación.
        """
        if not hasattr(world, 'weather'):
            return
        
        # Efectos de lluvia
        if world.weather == "rain":
            for _ in range(5):
                x = random.uniform(0, world.config.WORLD_SIZE[0])
                y = random.uniform(0, world.config.WORLD_SIZE[1])
                self.create_particle(x, y, "water", 
                                   velocity=(0, 3),
                                   custom_props={"size": 1, "life": 20})
        
        # Efectos de viento
        elif world.weather == "wind":
            for _ in range(3):
                x = random.uniform(0, world.config.WORLD_SIZE[0])
                y = random.uniform(0, world.config.WORLD_SIZE[1])
                self.create_particle(x, y, "stress", 
                                   velocity=(2, 0),
                                   custom_props={"size": 1, "life": 30})
    
    def get_particle_count(self):
        """Obtiene el número actual de partículas.
        
        Returns:
            int: Número de partículas activas.
        """
        return len(self.particles)
    
    def clear_all(self):
        """Elimina todas las partículas y emisores."""
        self.particles.clear()
        self.emitters.clear()
    
    def create_population_stress_visualization(self, world):
        """Crea visualización de estrés poblacional.
        
        Args:
            world (World): Mundo de la simulación.
        """
        if not hasattr(world, 'stress_psychology_system'):
            return
        
        report = world.stress_psychology_system.get_population_report()
        stress_percentage = report.get('social_breakdown_percentage', 0)
        
        if stress_percentage > 50:  # Alta tensión social
            # Crear partículas de estrés en áreas densas
            for nexus in world.entities:
                if nexus.alive and hasattr(nexus, 'stress_level'):
                    if nexus.stress_level > 0.6:
                        if random.random() < 0.1:  # 10% de probabilidad por frame
                            self.create_particle(
                                nexus.position[0] + random.uniform(-30, 30),
                                nexus.position[1] + random.uniform(-30, 30),
                                "stress",
                                velocity=(random.uniform(-0.5, 0.5), random.uniform(-1, 0))
                            )
