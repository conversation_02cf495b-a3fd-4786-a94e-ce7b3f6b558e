#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de gestión de recursos para el simulador Nexus.

Implementa mecánicas avanzadas de recursos incluyendo agua, herramientas,
materiales de construcción y gestión de inventario personal.
"""

import random
import math
from collections import defaultdict

class ResourceManagementSystem:
    """Sistema que gestiona recursos avanzados y inventarios personales."""
    
    def __init__(self, config):
        """Inicializa el sistema de gestión de recursos.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Tipos de recursos expandidos
        self.resource_types = {
            "food": {
                "value_range": (10, 30),
                "decay_rate": 0.01,  # Se pudre con el tiempo
                "stackable": True,
                "max_stack": 10
            },
            "water": {
                "value_range": (5, 15),
                "decay_rate": 0.005,  # Se evapora lentamente
                "stackable": True,
                "max_stack": 5
            },
            "wood": {
                "value_range": (1, 5),
                "decay_rate": 0.0,  # No se degrada
                "stackable": True,
                "max_stack": 20
            },
            "stone": {
                "value_range": (1, 3),
                "decay_rate": 0.0,
                "stackable": True,
                "max_stack": 15
            },
            "fiber": {
                "value_range": (1, 2),
                "decay_rate": 0.002,  # Se degrada lentamente
                "stackable": True,
                "max_stack": 25
            },
            "medicine": {
                "value_range": (20, 50),
                "decay_rate": 0.003,
                "stackable": True,
                "max_stack": 5
            },
            "tool": {
                "value_range": (50, 100),
                "decay_rate": 0.001,  # Se desgasta con uso
                "stackable": False,
                "durability": True
            }
        }
        
        # Necesidades de recursos por individuo
        self.resource_needs = {
            "food": {"daily": 2, "critical_threshold": 1},
            "water": {"daily": 3, "critical_threshold": 1},
            "shelter_materials": {"for_construction": 10}
        }
        
        # Herramientas y sus efectos
        self.tools = {
            "stone_axe": {
                "materials": {"stone": 2, "wood": 1, "fiber": 1},
                "effects": {"wood_gathering": 2.0, "hunting": 1.5},
                "durability": 100
            },
            "spear": {
                "materials": {"wood": 2, "stone": 1, "fiber": 1},
                "effects": {"hunting": 3.0, "defense": 2.0},
                "durability": 80
            },
            "basket": {
                "materials": {"fiber": 5, "wood": 1},
                "effects": {"carrying_capacity": 2.0},
                "durability": 150
            },
            "fire_starter": {
                "materials": {"wood": 2, "fiber": 2},
                "effects": {"fire_making": 5.0, "warmth": 2.0},
                "durability": 200
            }
        }
        
        # Recetas de construcción
        self.construction_recipes = {
            "simple_shelter": {
                "materials": {"wood": 8, "fiber": 5, "stone": 3},
                "capacity": 3,  # Número de individuos
                "comfort": 20,
                "protection": 15
            },
            "advanced_shelter": {
                "materials": {"wood": 15, "stone": 10, "fiber": 8},
                "capacity": 6,
                "comfort": 40,
                "protection": 30
            },
            "storage_pit": {
                "materials": {"stone": 5, "wood": 3},
                "storage_capacity": 50,
                "preservation": 0.5  # Reduce decay rate
            },
            "water_collector": {
                "materials": {"stone": 8, "fiber": 3},
                "water_generation": 2,  # Agua por día
                "efficiency": 1.0
            }
        }
    
    def initialize_inventory(self, nexus):
        """Inicializa el inventario personal de un Nexus.
        
        Args:
            nexus (Nexus): Nexus a inicializar.
        """
        if not hasattr(nexus, "inventory"):
            nexus.inventory = {
                "resources": defaultdict(int),  # {resource_type: quantity}
                "tools": [],  # Lista de herramientas con durabilidad
                "capacity": 10,  # Capacidad base de carga
                "current_load": 0
            }
        
        if not hasattr(nexus, "resource_needs"):
            nexus.resource_needs = {
                "food": 0,
                "water": 0,
                "last_consumption": {"food": 0, "water": 0}
            }
    
    def update(self, nexus, world):
        """Actualiza el sistema de recursos para un Nexus.
        
        Args:
            nexus (Nexus): Nexus a actualizar.
            world (World): Referencia al mundo.
        """
        if not nexus.alive:
            return
        
        # Inicializar inventario si es necesario
        self.initialize_inventory(nexus)
        
        # Actualizar necesidades de recursos
        self._update_resource_needs(nexus, world)
        
        # Degradar recursos perecederos
        self._degrade_perishable_resources(nexus)
        
        # Desgastar herramientas
        self._degrade_tools(nexus)
        
        # Gestionar consumo automático
        self._auto_consume_resources(nexus)
        
        # Evaluar necesidades críticas
        try:
            self._evaluate_critical_needs(nexus, world)
        except Exception as e:
            # Silently handle errors in critical needs evaluation
            pass
    
    def _update_resource_needs(self, nexus, world):
        """Actualiza las necesidades de recursos del Nexus.
        
        Args:
            nexus (Nexus): Nexus a actualizar.
            world (World): Referencia al mundo.
        """
        # Incrementar necesidades con el tiempo
        time_factor = 1.0
        
        # Ajustar por actividad (más actividad = más necesidades)
        if nexus.state in ["hunting", "exploring", "fleeing", "building"]:
            time_factor = 1.5
        elif nexus.state in ["resting", "idle"]:
            time_factor = 0.7
        
        # Incrementar necesidad de comida
        nexus.resource_needs["food"] += 0.02 * time_factor
        
        # Incrementar necesidad de agua (más rápido que comida)
        nexus.resource_needs["water"] += 0.03 * time_factor
        
        # Ajustar por clima
        if hasattr(world, "weather"):
            if world.weather in ["hot", "desert"]:
                nexus.resource_needs["water"] += 0.01
            elif world.weather in ["cold", "snowy"]:
                nexus.resource_needs["food"] += 0.01
    
    def _degrade_perishable_resources(self, nexus):
        """Degrada recursos perecederos en el inventario.
        
        Args:
            nexus (Nexus): Nexus cuyo inventario se degrada.
        """
        for resource_type, quantity in list(nexus.inventory["resources"].items()):
            if resource_type in self.resource_types:
                decay_rate = self.resource_types[resource_type]["decay_rate"]
                
                if decay_rate > 0 and quantity > 0:
                    # Calcular degradación
                    degradation = max(0, int(quantity * decay_rate))
                    
                    if degradation > 0:
                        nexus.inventory["resources"][resource_type] -= degradation
                        nexus.inventory["current_load"] -= degradation
                        
                        # Añadir memoria si pierde recursos significativos
                        if degradation >= 2:
                            nexus.add_memory(f"Perdió {degradation} unidades de {resource_type} por degradación")
    
    def _degrade_tools(self, nexus):
        """Degrada herramientas por uso.
        
        Args:
            nexus (Nexus): Nexus cuyas herramientas se degradan.
        """
        for tool in list(nexus.inventory["tools"]):
            # Degradación por uso activo
            if nexus.state in ["hunting", "gathering", "building"]:
                tool["durability"] -= random.uniform(0.5, 2.0)
            else:
                # Degradación natural mínima
                tool["durability"] -= random.uniform(0.1, 0.3)
            
            # Remover herramientas rotas
            if tool["durability"] <= 0:
                nexus.inventory["tools"].remove(tool)
                nexus.add_memory(f"Su {tool['type']} se rompió por el uso")
    
    def _auto_consume_resources(self, nexus):
        """Consume automáticamente recursos según las necesidades.
        
        Args:
            nexus (Nexus): Nexus que consume recursos.
        """
        # Consumir comida si tiene hambre y comida disponible
        if (nexus.resource_needs["food"] >= 1.0 and 
            nexus.inventory["resources"]["food"] > 0):
            
            consumed = min(nexus.inventory["resources"]["food"], 
                          int(nexus.resource_needs["food"]))
            
            nexus.inventory["resources"]["food"] -= consumed
            nexus.inventory["current_load"] -= consumed
            nexus.resource_needs["food"] -= consumed
            
            # Recuperar energía por comer
            energy_gain = consumed * 15
            nexus.energy = min(100, nexus.energy + energy_gain)
            
            nexus.add_memory(f"Consumió {consumed} unidades de comida")
        
        # Consumir agua si tiene sed y agua disponible
        if (nexus.resource_needs["water"] >= 1.0 and 
            nexus.inventory["resources"]["water"] > 0):
            
            consumed = min(nexus.inventory["resources"]["water"], 
                          int(nexus.resource_needs["water"]))
            
            nexus.inventory["resources"]["water"] -= consumed
            nexus.inventory["current_load"] -= consumed
            nexus.resource_needs["water"] -= consumed
            
            # Recuperar salud por beber agua
            health_gain = consumed * 5
            nexus.health = min(100, nexus.health + health_gain)
            
            nexus.add_memory(f"Consumió {consumed} unidades de agua")
    
    def _evaluate_critical_needs(self, nexus, world):
        """Evalúa necesidades críticas y ajusta comportamiento.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            world (World): Referencia al mundo.
        """
        # Necesidad crítica de comida
        if nexus.resource_needs["food"] >= 2.0:
            if nexus.inventory["resources"]["food"] == 0:
                # Cambiar estado a búsqueda urgente de comida
                if nexus.state not in ["seeking_food", "hunting", "gathering"]:
                    nexus.state = "seeking_food"
                    nexus.add_memory("Necesidad crítica de comida")
                
                # Aumentar desesperación
                nexus.emotions["fear"] = min(1.0, nexus.emotions.get("fear", 0) + 0.1)
        
        # Necesidad crítica de agua
        if nexus.resource_needs["water"] >= 2.0:
            if nexus.inventory["resources"]["water"] == 0:
                # Cambiar estado a búsqueda urgente de agua
                if nexus.state not in ["seeking_water"]:
                    nexus.state = "seeking_water"
                    nexus.add_memory("Necesidad crítica de agua")
                
                # Aumentar desesperación más que con comida
                nexus.emotions["fear"] = min(1.0, nexus.emotions.get("fear", 0) + 0.15)
    
    def collect_resource(self, nexus, resource_type, quantity, world):
        """Permite a un Nexus recolectar un recurso.
        
        Args:
            nexus (Nexus): Nexus que recolecta.
            resource_type (str): Tipo de recurso.
            quantity (int): Cantidad a recolectar.
            world (World): Referencia al mundo.
            
        Returns:
            bool: True si se recolectó exitosamente.
        """
        if not hasattr(nexus, "inventory"):
            self.initialize_inventory(nexus)
        
        # Verificar capacidad de carga
        if nexus.inventory["current_load"] + quantity > nexus.inventory["capacity"]:
            # Intentar recolectar solo lo que cabe
            available_space = nexus.inventory["capacity"] - nexus.inventory["current_load"]
            if available_space <= 0:
                nexus.add_memory(f"No pudo recolectar {resource_type}: inventario lleno")
                return False
            quantity = available_space
        
        # Añadir al inventario
        nexus.inventory["resources"][resource_type] += quantity
        nexus.inventory["current_load"] += quantity
        
        # Calcular eficiencia de recolección basada en herramientas
        efficiency = self._get_collection_efficiency(nexus, resource_type)
        actual_quantity = int(quantity * efficiency)
        
        nexus.add_memory(f"Recolectó {actual_quantity} unidades de {resource_type}")
        
        # Ganar experiencia en recolección
        if not hasattr(nexus, "skills"):
            nexus.skills = defaultdict(float)
        
        nexus.skills[f"{resource_type}_gathering"] += 0.1
        
        return True
    
    def _get_collection_efficiency(self, nexus, resource_type):
        """Calcula la eficiencia de recolección basada en herramientas y habilidades.
        
        Args:
            nexus (Nexus): Nexus recolector.
            resource_type (str): Tipo de recurso.
            
        Returns:
            float: Multiplicador de eficiencia.
        """
        efficiency = 1.0
        
        # Bonus por herramientas
        for tool in nexus.inventory.get("tools", []):
            tool_data = self.tools.get(tool["type"], {})
            effects = tool_data.get("effects", {})
            
            if f"{resource_type}_gathering" in effects:
                efficiency *= effects[f"{resource_type}_gathering"]
        
        # Bonus por habilidades
        if hasattr(nexus, "skills"):
            skill_level = nexus.skills.get(f"{resource_type}_gathering", 0)
            efficiency *= (1.0 + skill_level * 0.1)
        
        return efficiency
    
    def can_craft_tool(self, nexus, tool_type):
        """Verifica si un Nexus puede fabricar una herramienta.
        
        Args:
            nexus (Nexus): Nexus fabricante.
            tool_type (str): Tipo de herramienta.
            
        Returns:
            bool: True si puede fabricar la herramienta.
        """
        if tool_type not in self.tools:
            return False
        
        required_materials = self.tools[tool_type]["materials"]
        
        for material, required_qty in required_materials.items():
            if nexus.inventory["resources"].get(material, 0) < required_qty:
                return False
        
        return True
    
    def craft_tool(self, nexus, tool_type):
        """Fabrica una herramienta.
        
        Args:
            nexus (Nexus): Nexus fabricante.
            tool_type (str): Tipo de herramienta.
            
        Returns:
            bool: True si se fabricó exitosamente.
        """
        if not self.can_craft_tool(nexus, tool_type):
            return False
        
        # Consumir materiales
        required_materials = self.tools[tool_type]["materials"]
        for material, required_qty in required_materials.items():
            nexus.inventory["resources"][material] -= required_qty
            nexus.inventory["current_load"] -= required_qty
        
        # Crear herramienta
        tool = {
            "type": tool_type,
            "durability": self.tools[tool_type]["durability"]
        }
        
        nexus.inventory["tools"].append(tool)
        nexus.add_memory(f"Fabricó una {tool_type}")
        
        # Ganar experiencia en fabricación
        if not hasattr(nexus, "skills"):
            nexus.skills = defaultdict(float)
        
        nexus.skills["crafting"] += 0.2
        
        return True
