#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Módulo del mundo para el simulador Nexus.

Gestiona el entorno, los recursos y las interacciones entre entidades.
"""

import pygame
import random
import math
from pygame.locals import *

class Resource:
    """Clase que representa un recurso en el mundo (comida, agua, etc.)."""

    def __init__(self, resource_type, position, value=10):
        """Inicializa un nuevo recurso.

        Args:
            resource_type (str): Tipo de recurso ('food', 'water', etc.).
            position (tuple): Posición (x, y) del recurso.
            value (int): Valor nutricional o de utilidad del recurso.
        """
        self.type = resource_type
        self.position = list(position)
        self.nutritional_value = value
        self.size = 10
        self.consumed = False

        # Apariencia según tipo
        self.colors = {
            "food": (255, 200, 0),    # Amarillo (fruta)
            "water": (0, 191, 255),  # A<PERSON>l (agua)
            "wood": (139, 69, 19),   # <PERSON><PERSON><PERSON> (madera)
            "stone": (169, 169, 169) # <PERSON><PERSON> (piedra)
        }
        self.color = self.colors.get(self.type, (100, 100, 100))

    def consume(self):
        """Marca el recurso como consumido."""
        self.consumed = True

    def render(self, screen, camera_offset=(0, 0)):
        """Renderiza el recurso en la pantalla.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (tuple): Desplazamiento de la cámara (x, y).
        """
        # Calcular posición en pantalla
        screen_x = int(self.position[0] - camera_offset[0])
        screen_y = int(self.position[1] - camera_offset[1])

        # No renderizar si está fuera de la pantalla
        if (screen_x < -self.size or screen_x > screen.get_width() + self.size or
            screen_y < -self.size or screen_y > screen.get_height() + self.size):
            return

        # Dibujar según el tipo
        if self.type == "food":
            pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.size)
        elif self.type == "water":
            pygame.draw.rect(screen, self.color, (screen_x - self.size, screen_y - self.size,
                                                self.size * 2, self.size * 2))
        elif self.type == "wood":
            pygame.draw.polygon(screen, self.color, [
                (screen_x, screen_y - self.size),
                (screen_x + self.size, screen_y),
                (screen_x, screen_y + self.size),
                (screen_x - self.size, screen_y)
            ])
        else:  # Por defecto
            pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.size)


class Predator:
    """Clase que representa un depredador en el mundo."""

    def __init__(self, position, speed=1.5):
        """Inicializa un nuevo depredador.

        Args:
            position (tuple): Posición inicial (x, y).
            speed (float): Velocidad de movimiento.
        """
        self.position = list(position)
        self.velocity = [0, 0]
        self.speed = speed
        self.size = 25
        self.color = (200, 0, 0)  # Rojo
        self.target = None
        self.hunting_range = 150  # Reducido para dar más oportunidad a los Nexus
        self.attack_cooldown = 0  # Tiempo de espera entre ataques
        self.attack_cooldown_max = 100  # Tiempo máximo entre ataques

    def update(self, world):
        """Actualiza el estado del depredador.

        Args:
            world (World): Referencia al mundo.
        """
        # Actualizar cooldown de ataque
        if self.attack_cooldown > 0:
            self.attack_cooldown -= 1

        # Probabilidad de perder interés en la presa actual
        if self.target and random.random() < 0.005:  # 0.5% de probabilidad por tick
            self.target = None

        # Buscar presa si no tiene objetivo
        if not self.target:
            # Solo buscar presas ocasionalmente (menos agresivo)
            if random.random() < 0.3:  # 30% de probabilidad de buscar
                nearest_nexus = world.find_nearest_nexus(self.position)
                if nearest_nexus:
                    # Calcular distancia
                    distance = math.sqrt((self.position[0] - nearest_nexus.position[0])**2 +
                                        (self.position[1] - nearest_nexus.position[1])**2)

                    # Solo perseguir si está dentro del rango y con cierta probabilidad
                    if distance < self.hunting_range and random.random() < 0.5:
                        self.target = nearest_nexus
                    else:
                        # Movimiento aleatorio
                        self.random_movement()
                else:
                    # Movimiento aleatorio
                    self.random_movement()
            else:
                # Movimiento aleatorio
                self.random_movement()
        else:
            # Verificar si el objetivo sigue vivo
            if not self.target.alive:
                self.target = None
                return

            # Perseguir objetivo
            self.chase_target()

            # Verificar si ha alcanzado al objetivo
            distance = math.sqrt((self.position[0] - self.target.position[0])**2 +
                                (self.position[1] - self.target.position[1])**2)

            if distance < self.size + self.target.size and self.attack_cooldown == 0:
                # Atacar
                self.attack(self.target)
                # Establecer cooldown para el próximo ataque
                self.attack_cooldown = self.attack_cooldown_max

        # Actualizar posición
        self.position[0] += self.velocity[0]
        self.position[1] += self.velocity[1]

        # Mantener dentro de los límites del mundo circular
        if hasattr(world, 'is_in_world') and not world.is_in_world(self.position):
            # Si está fuera del mundo, aplicar fuerza hacia el centro
            if hasattr(world, 'get_border_force'):
                force = world.get_border_force(self.position)
                self.velocity[0] += force[0]
                self.velocity[1] += force[1]

                # Actualizar posición con la nueva velocidad
                self.position[0] += self.velocity[0]
                self.position[1] += self.velocity[1]

    def random_movement(self):
        """Genera un movimiento aleatorio."""
        # Cambiar dirección ocasionalmente
        if random.random() < 0.05:
            angle = random.uniform(0, 2 * math.pi)
            self.velocity[0] = math.cos(angle) * self.speed
            self.velocity[1] = math.sin(angle) * self.speed

    def chase_target(self):
        """Persigue al objetivo actual."""
        if not self.target:
            return

        # Calcular dirección hacia el objetivo
        direction = [self.target.position[0] - self.position[0],
                     self.target.position[1] - self.position[1]]

        # Normalizar el vector de dirección
        length = math.sqrt(direction[0]**2 + direction[1]**2)
        if length > 0:
            direction[0] /= length
            direction[1] /= length

        # Establecer velocidad
        self.velocity[0] = direction[0] * self.speed
        self.velocity[1] = direction[1] * self.speed

    def attack(self, nexus):
        """Ataca a un Nexus.

        Args:
            nexus (Nexus): Nexus a atacar.
        """
        # Infligir daño (reducido)
        damage = random.randint(5, 10)  # Daño aleatorio entre 5 y 10 (antes era 20 fijo)
        nexus.health -= damage

        # Aumentar miedo en el Nexus (reducido)
        nexus.emotions["fear"] = min(1.0, nexus.emotions["fear"] + 0.2)

        # Añadir recuerdo del ataque
        nexus.add_memory("Fue atacado por un depredador")

        # Probabilidad de que el depredador abandone después de atacar
        if random.random() < 0.3:  # 30% de probabilidad
            self.target = None
            # Alejarse después del ataque
            direction = [self.position[0] - nexus.position[0],
                        self.position[1] - nexus.position[1]]
            length = math.sqrt(direction[0]**2 + direction[1]**2)
            if length > 0:
                direction[0] /= length
                direction[1] /= length
            self.velocity[0] = direction[0] * self.speed
            self.velocity[1] = direction[1] * self.speed

        # Verificar si el Nexus ha muerto
        if nexus.health <= 0:
            nexus.die()
            self.target = None

    def die(self):
        """Gestiona la muerte del depredador."""
        # Detener movimiento
        self.velocity = [0, 0]

        # Cambiar color a gris (muerto)
        self.color = (150, 150, 150)

        # Añadir al sistema de descomposición si existe
        if hasattr(self, "world") and self.world and hasattr(self.world, "decomposition_system"):
            self.world.decomposition_system.add_corpse(self)

        # Eliminar de la lista de depredadores
        if hasattr(self, "world") and self.world:
            if self in self.world.predators:
                self.world.predators.remove(self)

    def render(self, screen, camera_offset=(0, 0)):
        """Renderiza el depredador en la pantalla.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (tuple): Desplazamiento de la cámara (x, y).
        """
        # Calcular posición en pantalla
        screen_x = int(self.position[0] - camera_offset[0])
        screen_y = int(self.position[1] - camera_offset[1])

        # No renderizar si está fuera de la pantalla
        if (screen_x < -self.size or screen_x > screen.get_width() + self.size or
            screen_y < -self.size or screen_y > screen.get_height() + self.size):
            return

        # Dibujar cuerpo
        pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.size)

        # Dibujar ojos
        eye_offset = 8
        pygame.draw.circle(screen, (255, 255, 255),
                          (screen_x - eye_offset, screen_y - eye_offset), 5)
        pygame.draw.circle(screen, (255, 255, 255),
                          (screen_x + eye_offset, screen_y - eye_offset), 5)

        # Pupilas (mirando hacia el objetivo si existe)
        pupil_offset = 2
        if self.target:
            # Dirección hacia el objetivo
            direction = [self.target.position[0] - self.position[0],
                         self.target.position[1] - self.position[1]]
            length = math.sqrt(direction[0]**2 + direction[1]**2)
            if length > 0:
                direction[0] /= length
                direction[1] /= length

            pupil_x_left = screen_x - eye_offset + direction[0] * pupil_offset
            pupil_y_left = screen_y - eye_offset + direction[1] * pupil_offset
            pupil_x_right = screen_x + eye_offset + direction[0] * pupil_offset
            pupil_y_right = screen_y - eye_offset + direction[1] * pupil_offset
        else:
            pupil_x_left = screen_x - eye_offset
            pupil_y_left = screen_y - eye_offset
            pupil_x_right = screen_x + eye_offset
            pupil_y_right = screen_y - eye_offset

        pygame.draw.circle(screen, (0, 0, 0), (int(pupil_x_left), int(pupil_y_left)), 2)
        pygame.draw.circle(screen, (0, 0, 0), (int(pupil_x_right), int(pupil_y_right)), 2)


class World:
    """Clase que representa el mundo y gestiona entidades y recursos."""
    def __init__(self, config):
        self.config = config
        self.entities = []
        self.resources = []
        self.predators = []
        self.tribes = []  # Lista de tribus
        self.time = 0
        self.entity_id_counter = 0

        # Límite de entidades
        self.max_entities = 314  # Límite máximo de Nexus y depredadores combinados

        # Sistema de mapa circular
        self.world_radius = 5000  # Radio del mundo circular
        self.center_x = 0  # Centro del mundo (X)
        self.center_y = 0  # Centro del mundo (Y)
        self.border_width = 200  # Ancho del borde del mundo

        # Chunks para el mapa circular
        self.chunks = {}  # Diccionario de chunks (regiones del mapa)
        self.chunk_size = 500  # Tamaño de cada chunk
        self.active_chunks = set()  # Chunks actualmente activos
        self.chunk_load_distance = 3  # Distancia de carga de chunks
        self.world_seed = random.randint(1, 1000000)  # Semilla para generación procedural

        # Configurar tamaño del mundo para renderizado
        self.config.WORLD_SIZE = (self.world_radius * 2, self.world_radius * 2)

        # Elementos naturales
        self.trees = []  # Lista de árboles
        self.plants = []  # Lista de plantas
        self.water_bodies = []  # Lista de cuerpos de agua
        self.animals = []  # Lista de animales pequeños (presas)

        # Sistema de refugios
        from society.shelter import ShelterSystem
        self.shelter_system = ShelterSystem(self)

        # Sistema de lenguaje
        from society.language import LanguageSystem
        self.language_system = LanguageSystem(self)

        # Sistema de descomposición
        from environment.decomposition import DecompositionSystem
        self.decomposition_system = DecompositionSystem(self)

        # Sistema de salud y enfermedades
        from nexus.health import HealthSystem
        self.health_system = HealthSystem(self.config)

        # Sistemas avanzados para autonomía completa (Universe 25)
        from nexus.autonomous_ai import AutonomousAI
        from nexus.stress_psychology import StressPsychologySystem
        from nexus.resource_management import ResourceManagementSystem
        from nexus.territory_system import TerritorySystem

        self.autonomous_ai_system = AutonomousAI(self.config)
        self.stress_psychology_system = StressPsychologySystem(self.config)
        self.resource_management_system = ResourceManagementSystem(self.config)
        self.territory_system = TerritorySystem(self.config)

        # Sistema de tecnología
        from society.technology import TechnologySystem
        self.technology_system = TechnologySystem(self.config)

        # Sistema de aprendizaje
        from nexus.learning import LearningSystem
        self.learning_system = LearningSystem(self.config)

        # Sistema de ideas
        from nexus.ideas import IdeaSystem
        self.idea_system = IdeaSystem(self.config)

        # Sistema de proyectos
        from society.projects import ProjectSystem
        self.project_system = ProjectSystem(self.config)

        # Sistema de emociones avanzado
        from nexus.emotions import EmotionSystem
        self.emotion_system = EmotionSystem(self.config)

        # Sistema de nombres
        from nexus.naming import NameGenerator
        self.name_generator = NameGenerator()

        # Sistema de reproducción avanzado
        from nexus.reproduction import ReproductionSystem
        self.reproduction_system = ReproductionSystem(self.config)

        # Sistema de renderizado avanzado
        from nexus.rendering import NexusRenderer
        self.nexus_renderer = NexusRenderer(self.config)

        # Sistema de física avanzado
        from nexus.physics import PhysicsSystem
        self.physics_system = PhysicsSystem()

        # Coordenadas 3D para entidades
        self.use_3d = True  # Indica si se usa el sistema 3D

        # Sistema de tiempo real mejorado
        self.real_time_factor = 1.0  # Factor de tiempo real (1.0 = normal, 2.0 = doble velocidad)
        self.real_time_enabled = True  # Activado por defecto
        self.real_time_start = 0  # Tiempo de inicio para cálculos de tiempo real
        self.real_time_paused = False  # Indica si el tiempo real está pausado
        self.real_time_elapsed = 0  # Tiempo real transcurrido (en milisegundos)

        # Escala de tiempo (1 minuto real = 5 años simulados)
        # 5 años = 5 * 365 días = 1825 días
        # 1 día = 24 horas = 1440 minutos
        # 1825 días = 2,628,000 minutos simulados
        # Ratio: 2,628,000 / 1 = 2,628,000
        self.time_scale = 2628000.0  # Minutos simulados por minuto real

        # Tiempo simulado
        self.simulated_time = {
            "minutes": 0,
            "hours": 0,
            "days": 0,
            "months": 0,
            "years": 0
        }

        # Sistema de eventos y notificaciones
        self.events = []  # Lista de eventos importantes
        self.event_duration = 200  # Duración de los eventos en ticks

        # Sistema de clima mejorado
        self.weather = "clear"  # Estado actual del clima
        self.weather_intensity = 0.5  # Intensidad del clima actual (0.0 a 1.0)
        self.weather_duration = 0  # Duración restante del clima actual
        self.season = "summer"  # Estación actual
        self.season_day = 0  # Día actual de la estación
        self.season_length = 2000  # Duración de cada estación en ticks

        # Tipos de clima por estación con probabilidades
        self.seasonal_weather = {
            "spring": {"clear": 0.5, "rain": 0.3, "fog": 0.2},
            "summer": {"clear": 0.7, "rain": 0.2, "heat_wave": 0.1},
            "autumn": {"clear": 0.4, "rain": 0.3, "fog": 0.2, "wind": 0.1},
            "winter": {"clear": 0.3, "snow": 0.4, "blizzard": 0.2, "fog": 0.1}
        }

        # Efectos del clima en los Nexus
        self.weather_effects = {
            "clear": {"energy": 0.0, "speed": 0.0, "health": 0.0},
            "rain": {"energy": -0.1, "speed": -0.2, "health": 0.0},
            "fog": {"energy": 0.0, "speed": -0.3, "health": 0.0},
            "wind": {"energy": -0.1, "speed": -0.1, "health": 0.0},
            "snow": {"energy": -0.2, "speed": -0.3, "health": -0.1},
            "blizzard": {"energy": -0.3, "speed": -0.5, "health": -0.2},
            "heat_wave": {"energy": -0.3, "speed": -0.2, "health": -0.1}
        }

        # Conocimientos disponibles para descubrir
        self.available_knowledge = [
            "Hacer fuego",
            "Construir refugio",
            "Fabricar lanza",
            "Recolectar bayas",
            "Pescar",
            "Cazar en grupo",
            "Curar heridas",
            "Cultivar plantas",
            "Domesticar animales",
            "Crear herramientas de piedra",
            "Tejer cestas",
            "Preservar alimentos",
            "Construir balsas",
            "Extraer minerales",
            "Fundir metales"
        ]

        # Tecnologías descubiertas por la civilización
        self.discovered_technologies = set()

        # Generar recursos iniciales
        self.generate_initial_resources()

        # Generar depredadores iniciales
        self.generate_predators()

        # Sistema de cámara mejorado
        self.camera_offset = [0, 0]
        self.camera_target = None
        self.camera_zoom = 1.0  # Factor de zoom (1.0 = normal)
        self.camera_drag = False  # Indica si se está arrastrando la cámara
        self.camera_drag_start = [0, 0]  # Posición inicial del arrastre

        # Inicializar sistemas sociales
        from society.beliefs import BeliefSystem
        self.belief_system = BeliefSystem(self)

        from society.culture import CultureSystem
        self.culture_system = CultureSystem(self)

    def get_next_id(self):
        """Obtiene un ID único para una nueva entidad.

        Returns:
            int: ID único.
        """
        self.entity_id_counter += 1
        return self.entity_id_counter

    def generate_initial_resources(self):
        """Genera los recursos iniciales y elementos naturales en el mundo."""
        from environment.natural_elements import Tree, Plant, WaterBody, Animal

        # Generar recursos básicos
        for _ in range(self.config.MAX_RESOURCES // 4):  # Reducir cantidad para no saturar
            # Generar posición dentro del mundo circular
            while True:
                x = random.randint(-self.world_radius + 100, self.world_radius - 100)
                y = random.randint(-self.world_radius + 100, self.world_radius - 100)
                if self.is_in_world((x, y)):
                    break

            resource_type = random.choice(["food", "water", "wood", "stone"])
            value = random.randint(5, 15)
            self.resources.append(Resource(resource_type, (x, y), value))

        # Generar árboles
        num_trees = 200  # Cantidad de árboles
        for _ in range(num_trees):
            # Generar posición dentro del mundo circular
            while True:
                x = random.randint(-self.world_radius + 100, self.world_radius - 100)
                y = random.randint(-self.world_radius + 100, self.world_radius - 100)
                if self.is_in_world((x, y)):
                    break

            tree_type = random.choice(["oak", "pine", "fruit"])
            size = random.randint(20, 40)
            tree = Tree((x, y), size, tree_type)
            self.trees.append(tree)

        # Generar plantas
        num_plants = 300  # Cantidad de plantas
        for _ in range(num_plants):
            # Generar posición dentro del mundo circular
            while True:
                x = random.randint(-self.world_radius + 100, self.world_radius - 100)
                y = random.randint(-self.world_radius + 100, self.world_radius - 100)
                if self.is_in_world((x, y)):
                    break

            plant_type = random.choice(["bush", "flower", "herb"])
            plant = Plant((x, y), plant_type)
            self.plants.append(plant)

        # Generar cuerpos de agua
        num_water_bodies = 20  # Cantidad de cuerpos de agua
        for _ in range(num_water_bodies):
            # Generar posición dentro del mundo circular
            while True:
                x = random.randint(-self.world_radius + 200, self.world_radius - 200)
                y = random.randint(-self.world_radius + 200, self.world_radius - 200)
                if self.is_in_world((x, y)):
                    break

            water_type = random.choice(["pond", "lake", "river"])
            size = random.randint(30, 100)
            water_body = WaterBody((x, y), water_type, size)
            self.water_bodies.append(water_body)

        # Generar animales
        num_animals = 100  # Cantidad de animales
        for _ in range(num_animals):
            # Generar posición dentro del mundo circular
            while True:
                x = random.randint(-self.world_radius + 100, self.world_radius - 100)
                y = random.randint(-self.world_radius + 100, self.world_radius - 100)
                if self.is_in_world((x, y)):
                    break

            animal_type = random.choice(["rabbit", "deer", "bird"])
            animal = Animal((x, y), animal_type)
            self.animals.append(animal)

    def generate_predators(self):
        """Genera los depredadores iniciales en el mundo."""
        # Reducir la cantidad inicial de depredadores
        num_predators = max(1, self.config.MAX_PREDATORS // 2)  # La mitad de los configurados, mínimo 1

        for _ in range(num_predators):
            # Colocar depredadores lejos del centro donde comienza el primer Nexus
            margin = 300  # Margen desde el centro
            center_x, center_y = self.config.WORLD_SIZE[0] // 2, self.config.WORLD_SIZE[1] // 2

            # Generar posición alejada del centro
            while True:
                x = random.randint(0, self.config.WORLD_SIZE[0])
                y = random.randint(0, self.config.WORLD_SIZE[1])
                distance_from_center = math.sqrt((x - center_x)**2 + (y - center_y)**2)
                if distance_from_center > margin:
                    break

            speed = random.uniform(0.8, 1.5)  # Velocidad reducida

            self.predators.append(Predator((x, y), speed))

    def initialize_nexus(self, nexus):
        """Inicializa un Nexus con atributos adicionales y sistemas avanzados.

        Args:
            nexus (Nexus): Nexus a inicializar.
        """
        # Inicializar atributos de creatividad si no existen
        if "creativity" not in nexus.attributes:
            # Usar número áureo (PHI) para generar creatividad
            phi = (1 + 5 ** 0.5) / 2  # ≈ 1.618
            base_creativity = random.uniform(0.3, 0.7)
            intelligence_factor = nexus.attributes.get("intelligence", 0.5) * 0.3
            curiosity_factor = nexus.attributes.get("curiosity", 0.5) * 0.2

            # Fórmula que usa PHI para balancear los factores
            creativity = (base_creativity + intelligence_factor + curiosity_factor) / (1 + phi/10)
            nexus.attributes["creativity"] = max(0.1, min(0.9, creativity))

        # Inicializar emociones avanzadas
        if hasattr(self, "emotion_system"):
            self.emotion_system.initialize_emotions(nexus)

        # Inicializar memoria si no existe
        if not hasattr(nexus, "memory"):
            nexus.memory = []

        # Inicializar ideas si no existen
        if not hasattr(nexus, "ideas"):
            nexus.ideas = []

        # Inicializar referencia al mundo
        nexus.world = self

        # Inicializar conocimientos básicos aleatorios
        if not nexus.knowledge and self.available_knowledge:
            # Dar 1-2 conocimientos iniciales aleatorios
            num_initial_knowledge = random.randint(1, 2)
            initial_knowledge = random.sample(self.available_knowledge, min(num_initial_knowledge, len(self.available_knowledge)))
            for knowledge in initial_knowledge:
                nexus.knowledge.add(knowledge)

        # Añadir recuerdo de inicialización
        nexus.add_memory("Comenzó su existencia en el mundo")

    def add_entity(self, entity):
        """Añade una entidad al mundo.

        Args:
            entity: Entidad a añadir (Nexus).
        """
        # Inicializar Nexus con sistemas avanzados
        self.initialize_nexus(entity)

        # Añadir a la lista de entidades
        self.entities.append(entity)

    def update(self):
        """Actualiza el estado del mundo en cada tick."""
        # Incrementar tiempo según el factor de tiempo real si está activado
        if self.real_time_enabled and not self.real_time_paused:
            current_time = pygame.time.get_ticks()
            if self.real_time_start == 0:
                self.real_time_start = current_time
            else:
                # Calcular tiempo transcurrido desde el último update
                elapsed = current_time - self.real_time_start
                self.real_time_elapsed += elapsed
                # Actualizar tiempo de inicio para el próximo cálculo
                self.real_time_start = current_time

                # Aplicar factor de tiempo real a la edad de los Nexus
                time_increment = max(1, int(self.real_time_factor))
                self.time += time_increment

                # Actualizar tiempo simulado
                self.update_simulated_time(elapsed / 1000.0)  # Convertir a segundos
        else:
            # Incremento normal de tiempo
            self.time += 1

            # Actualizar tiempo simulado con un valor fijo pequeño
            self.update_simulated_time(1/60.0)  # Aproximadamente un frame a 60 FPS

        # Actualizar chunks activos basados en la posición de la cámara
        self.update_active_chunks()

        # Actualizar entidades
        for entity in self.entities:
            if entity.alive:
                # Si el tiempo real está activado, actualizar la edad según el factor
                if self.real_time_enabled and not self.real_time_paused:
                    entity.age += max(0, int(self.real_time_factor - 1))
                entity.update(self)

        # Actualizar depredadores
        for predator in self.predators:
            predator.update(self)

        # Reproducción de depredadores
        self.handle_predator_reproduction()

        # Verificar si hay suficientes Nexus vivos, si no, aumentar probabilidad de reproducción
        alive_nexus = [n for n in self.entities if n.alive]
        if len(alive_nexus) < 3:
            # Dar un impulso a la reproducción cuando hay pocos Nexus
            for nexus in alive_nexus:
                if nexus.energy > self.config.NEXUS_REPRODUCTION_ENERGY_THRESHOLD * 0.8:
                    nexus.energy = self.config.NEXUS_MAX_ENERGY * 0.9  # Dar energía extra para reproducción

        # Eliminar recursos consumidos
        self.resources = [r for r in self.resources if not r.consumed]

        # Generar nuevos recursos ocasionalmente
        if (len(self.resources) < self.config.MAX_RESOURCES and
                random.random() < self.config.RESOURCE_RESPAWN_RATE):
            self.spawn_resource()

        # Cambiar clima ocasionalmente
        if random.random() < self.config.WEATHER_CHANGE_RATE:
            self.change_weather()
        else:
            # Aplicar efectos del clima actual
            self.apply_weather_effects()

        # Actualizar elementos naturales
        self.update_natural_elements()

        # Actualizar sistema de refugios
        self.shelter_system.update()

        # Actualizar sistema de lenguaje
        self.language_system.update()

        # Actualizar sistema de descomposición
        self.decomposition_system.update()

        # Actualizar sistema de salud y enfermedades
        for nexus in self.entities:
            if nexus.alive:
                self.health_system.update(nexus, self)

        # Actualizar sistemas avanzados de autonomía (Universe 25)
        self.stress_psychology_system.update(self)
        self.territory_system.update(self)

        # Actualizar sistema de tecnología
        self.technology_system.update(self)

        # Actualizar sistema de ideas
        self.idea_system.update(self)

        # Actualizar sistema de proyectos
        self.project_system.update(self)

        # Actualizar sistema de reproducción avanzado
        self.reproduction_system.update(self)

        # Actualizar sistema de aprendizaje y emociones para cada Nexus
        for nexus in self.entities:
            if nexus.alive:
                # Actualizar memoria
                self.learning_system.update_memory(nexus)

                # Aplicar beneficios de conocimientos
                self.learning_system.apply_knowledge_benefits(nexus, self)

                # Decidir compartir conocimiento
                if random.random() < 0.01:  # Limitar frecuencia para rendimiento
                    self.learning_system.decide_knowledge_sharing(nexus, self)

                # Aplicar beneficios de la era tecnológica
                self.technology_system.apply_era_benefits(nexus, self)

                # Actualizar emociones avanzadas
                self.emotion_system.update(nexus, self)

                # Compartir ideas ocasionalmente
                if random.random() < 0.005:  # 0.5% por tick
                    # Buscar Nexus cercano para compartir
                    nearby_nexus = self.get_nearby_nexus(nexus.position, 100, exclude=nexus)
                    if nearby_nexus:
                        target = random.choice(nearby_nexus)
                        # Intentar compartir idea
                        if hasattr(nexus, "ideas") and nexus.ideas:
                            self.idea_system.share_idea(nexus, target)

        # Actualizar sistema de creencias si está inicializado
        if self.belief_system:
            self.belief_system.update()

        # Actualizar cámara si hay un objetivo
        self.update_camera()

        # Verificar límite de entidades
        self.enforce_entity_limits()

        # Actualizar eventos
        self.update_events()

    def update_natural_elements(self):
        """Actualiza todos los elementos naturales del mundo."""
        # Actualizar árboles
        for tree in self.trees:
            tree.update(self)

        # Actualizar plantas
        for plant in self.plants[:]:  # Usar copia para poder eliminar durante la iteración
            plant.update(self)

        # Actualizar cuerpos de agua
        for water_body in self.water_bodies:
            water_body.update(self)

        # Actualizar animales
        for animal in self.animals[:]:  # Usar copia para poder eliminar durante la iteración
            animal.update(self)
            # Eliminar animales muertos después de un tiempo
            if not animal.is_alive and hasattr(animal, 'age_since_death'):
                animal.age_since_death += 1
                if animal.age_since_death > 100:  # Eliminar después de 100 ticks
                    self.animals.remove(animal)
            elif not animal.is_alive:
                animal.age_since_death = 0

        # Generar nuevos animales ocasionalmente
        if len(self.animals) < 100 and random.random() < 0.01:
            self.spawn_animal()

    def spawn_animal(self):
        """Genera un nuevo animal en el mundo."""
        from environment.natural_elements import Animal

        # Generar posición dentro del mundo circular
        while True:
            x = random.randint(-self.world_radius + 100, self.world_radius - 100)
            y = random.randint(-self.world_radius + 100, self.world_radius - 100)
            if self.is_in_world((x, y)):
                break

        animal_type = random.choice(["rabbit", "deer", "bird"])
        animal = Animal((x, y), animal_type)
        self.animals.append(animal)

    def add_event(self, message):
        """Añade un evento importante al mundo.

        Args:
            message (str): Mensaje del evento.
        """
        self.events.append({
            "message": message,
            "time": self.time,
            "duration": self.event_duration
        })

    def update_simulated_time(self, real_seconds):
        """Actualiza el tiempo simulado basado en el tiempo real transcurrido.

        Args:
            real_seconds (float): Tiempo real transcurrido en segundos.
        """
        # Calcular minutos simulados
        simulated_minutes = real_seconds * self.time_scale * self.real_time_factor

        # Actualizar minutos
        self.simulated_time["minutes"] += simulated_minutes

        # Actualizar horas
        while self.simulated_time["minutes"] >= 60:
            self.simulated_time["minutes"] -= 60
            self.simulated_time["hours"] += 1

            # Notificar cada hora completa
            if self.simulated_time["hours"] % 6 == 0:  # Cada 6 horas
                self.add_event(f"Hora {self.simulated_time['hours'] % 24}:00")

        # Actualizar días
        while self.simulated_time["hours"] >= 24:
            self.simulated_time["hours"] -= 24
            self.simulated_time["days"] += 1

            # Notificar cada día completo
            self.add_event(f"Día {self.simulated_time['days']} completado")

        # Actualizar meses (aproximadamente 30 días)
        while self.simulated_time["days"] >= 30:
            self.simulated_time["days"] -= 30
            self.simulated_time["months"] += 1

            # Notificar cada mes completo
            self.add_event(f"Mes {self.simulated_time['months']} completado")

        # Actualizar años
        while self.simulated_time["months"] >= 12:
            self.simulated_time["months"] -= 12
            self.simulated_time["years"] += 1

            # Notificar cada año completo
            self.add_event(f"¡Año {self.simulated_time['years']} completado!")

            # Aplicar efectos de envejecimiento a los Nexus
            for nexus in self.entities:
                if nexus.alive:
                    # Añadir recuerdo del paso del año
                    nexus.add_memory(f"Vivió durante el año {self.simulated_time['years']}")

    def update_events(self):
        """Actualiza los eventos del mundo."""
        # Reducir duración de los eventos
        for event in self.events:
            event["duration"] -= 1

        # Eliminar eventos expirados
        self.events = [e for e in self.events if e["duration"] > 0]

    def render_events(self, screen, font):
        """Renderiza los eventos activos en la pantalla.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            font: Fuente para el texto.
        """
        import pygame

        # Renderizar eventos recientes
        y_offset = 50
        for event in self.events:
            # Calcular opacidad basada en duración restante
            alpha = min(255, int(255 * (event["duration"] / self.event_duration)))

            # Renderizar texto con sombra para mejor legibilidad
            text_shadow = font.render(event["message"], True, (0, 0, 0))
            text = font.render(event["message"], True, (255, 220, 100))

            # Aplicar opacidad
            text_shadow.set_alpha(alpha)
            text.set_alpha(alpha)

            # Posición centrada horizontalmente
            x = (screen.get_width() - text.get_width()) // 2

            # Renderizar sombra y texto
            screen.blit(text_shadow, (x + 2, y_offset + 2))
            screen.blit(text, (x, y_offset))

            y_offset += 30

    def enforce_entity_limits(self):
        """Limita el número de entidades en el mundo para evitar sobrecarga."""
        # Contar entidades vivas
        alive_nexus = [n for n in self.entities if n.alive]
        total_entities = len(alive_nexus) + len(self.predators)

        # Si excede el límite, eliminar algunas entidades
        if total_entities > self.max_entities:
            excess = total_entities - self.max_entities

            # Primero intentar eliminar depredadores
            if len(self.predators) > excess:
                # Eliminar los depredadores más débiles
                self.predators.sort(key=lambda p: p.health)
                self.predators = self.predators[excess:]
            else:
                # Eliminar algunos depredadores
                predators_to_remove = min(len(self.predators), excess // 2)
                if predators_to_remove > 0:
                    self.predators.sort(key=lambda p: p.health)
                    self.predators = self.predators[predators_to_remove:]

                # Eliminar algunos Nexus (los más débiles y viejos)
                nexus_to_remove = excess - predators_to_remove
                if nexus_to_remove > 0 and alive_nexus:
                    # Ordenar por salud y edad (priorizar los más débiles y viejos)
                    alive_nexus.sort(key=lambda n: (n.health, -n.age))

                    # Marcar como muertos (no eliminar de la lista para mantener referencias)
                    for i in range(min(nexus_to_remove, len(alive_nexus))):
                        if not alive_nexus[i].is_leader:  # Evitar eliminar líderes si es posible
                            alive_nexus[i].die()

    def update_active_chunks(self):
        """Actualiza los chunks activos basados en la posición de la cámara."""
        # Obtener el chunk central basado en la posición de la cámara
        center_x = int(self.camera_offset[0] + self.config.SCREEN_WIDTH / (2 * self.camera_zoom))
        center_y = int(self.camera_offset[1] + self.config.SCREEN_HEIGHT / (2 * self.camera_zoom))

        center_chunk_x = center_x // self.chunk_size
        center_chunk_y = center_y // self.chunk_size

        # Determinar chunks activos
        new_active_chunks = set()
        for dx in range(-self.chunk_load_distance, self.chunk_load_distance + 1):
            for dy in range(-self.chunk_load_distance, self.chunk_load_distance + 1):
                chunk_x = center_chunk_x + dx
                chunk_y = center_chunk_y + dy
                chunk_key = (chunk_x, chunk_y)
                new_active_chunks.add(chunk_key)

                # Generar chunk si no existe
                if chunk_key not in self.chunks:
                    self.generate_chunk(chunk_x, chunk_y)

        # Desactivar chunks que ya no están en rango
        chunks_to_unload = self.active_chunks - new_active_chunks
        for chunk_key in chunks_to_unload:
            self.unload_chunk(chunk_key)

        # Activar nuevos chunks
        chunks_to_load = new_active_chunks - self.active_chunks
        for chunk_key in chunks_to_load:
            self.load_chunk(chunk_key)

        # Actualizar conjunto de chunks activos
        self.active_chunks = new_active_chunks

    def generate_chunk(self, chunk_x, chunk_y):
        """Genera un nuevo chunk en la posición especificada.

        Args:
            chunk_x (int): Coordenada X del chunk.
            chunk_y (int): Coordenada Y del chunk.
        """
        chunk_key = (chunk_x, chunk_y)

        # Inicializar chunk
        self.chunks[chunk_key] = {
            "resources": [],
            "predators": []
        }

        # Generar recursos para el chunk
        chunk_seed = self.world_seed + hash(chunk_key)
        random.seed(chunk_seed)

        # Posición base del chunk
        base_x = chunk_x * self.chunk_size
        base_y = chunk_y * self.chunk_size

        # Generar recursos
        num_resources = random.randint(5, 15)
        for _ in range(num_resources):
            x = base_x + random.randint(0, self.chunk_size)
            y = base_y + random.randint(0, self.chunk_size)
            resource_type = random.choice(["food", "water", "wood", "stone"])
            value = random.randint(5, 15)

            resource = Resource(resource_type, (x, y), value)
            self.chunks[chunk_key]["resources"].append(resource)

        # Generar depredadores (menos frecuentes)
        if random.random() < 0.3:  # 30% de probabilidad de tener depredadores
            num_predators = random.randint(0, 2)
            for _ in range(num_predators):
                x = base_x + random.randint(0, self.chunk_size)
                y = base_y + random.randint(0, self.chunk_size)
                speed = random.uniform(0.8, 1.5)

                predator = Predator((x, y), speed)
                predator.health = random.randint(50, 100)
                predator.damage = random.randint(5, 15)
                self.chunks[chunk_key]["predators"].append(predator)

        # Restaurar semilla aleatoria
        random.seed()

    def load_chunk(self, chunk_key):
        """Carga un chunk en el mundo.

        Args:
            chunk_key (tuple): Clave del chunk (x, y).
        """
        if chunk_key in self.chunks:
            # Añadir recursos del chunk al mundo
            for resource in self.chunks[chunk_key]["resources"]:
                if not resource.consumed:
                    self.resources.append(resource)

            # Añadir depredadores del chunk al mundo
            for predator in self.chunks[chunk_key]["predators"]:
                self.predators.append(predator)

    def unload_chunk(self, chunk_key):
        """Descarga un chunk del mundo.

        Args:
            chunk_key (tuple): Clave del chunk (x, y).
        """
        if chunk_key in self.chunks:
            # Guardar estado de los recursos
            self.chunks[chunk_key]["resources"] = [r for r in self.resources
                                                if self.is_in_chunk(r.position, chunk_key)]

            # Guardar estado de los depredadores
            self.chunks[chunk_key]["predators"] = [p for p in self.predators
                                                 if self.is_in_chunk(p.position, chunk_key)]

            # Eliminar recursos y depredadores del mundo
            self.resources = [r for r in self.resources
                            if not self.is_in_chunk(r.position, chunk_key)]
            self.predators = [p for p in self.predators
                             if not self.is_in_chunk(p.position, chunk_key)]

    def is_in_chunk(self, position, chunk_key):
        """Verifica si una posición está dentro de un chunk.

        Args:
            position (tuple): Posición (x, y) a verificar.
            chunk_key (tuple): Clave del chunk (x, y).

        Returns:
            bool: True si la posición está en el chunk, False en caso contrario.
        """
        chunk_x, chunk_y = chunk_key
        base_x = chunk_x * self.chunk_size
        base_y = chunk_y * self.chunk_size

        return (base_x <= position[0] < base_x + self.chunk_size and
                base_y <= position[1] < base_y + self.chunk_size)

    def is_in_world(self, position):
        """Verifica si una posición está dentro del mundo circular.

        Args:
            position (tuple): Posición (x, y) a verificar.

        Returns:
            bool: True si la posición está en el mundo, False en caso contrario.
        """
        # Calcular distancia al centro
        dx = position[0] - self.center_x
        dy = position[1] - self.center_y
        distance = math.sqrt(dx*dx + dy*dy)

        # Verificar si está dentro del radio del mundo (excluyendo el borde)
        return distance <= self.world_radius * 0.95

    def is_near_border(self, position):
        """Verifica si una posición está cerca del borde del mundo.

        Args:
            position (tuple): Posición (x, y) a verificar.

        Returns:
            bool: True si la posición está cerca del borde, False en caso contrario.
        """
        # Calcular distancia al centro
        dx = position[0] - self.center_x
        dy = position[1] - self.center_y
        distance = math.sqrt(dx*dx + dy*dy)

        # Verificar si está en la zona de borde (entre el 90% y 95% del radio)
        inner_limit = self.world_radius * 0.90
        outer_limit = self.world_radius * 0.95
        return inner_limit <= distance <= outer_limit

    def get_border_force(self, position):
        """Calcula la fuerza de repulsión del borde del mundo.

        Args:
            position (tuple): Posición (x, y) a verificar.

        Returns:
            tuple: Vector de fuerza (fx, fy) que empuja hacia el centro.
        """
        # Calcular vector desde el centro
        dx = position[0] - self.center_x
        dy = position[1] - self.center_y
        distance = math.sqrt(dx*dx + dy*dy)

        # Límites del borde
        inner_limit = self.world_radius * 0.90
        outer_limit = self.world_radius * 0.95

        # Si no está cerca del borde, no hay fuerza
        if distance < inner_limit:
            return (0, 0)

        # Normalizar el vector
        if distance > 0:
            dx /= distance
            dy /= distance

        # Calcular intensidad de la fuerza (aumenta al acercarse al borde exterior)
        if distance > outer_limit:
            # Si está fuera del límite, fuerza máxima
            force_intensity = 3.0
        else:
            # Fuerza proporcional a la distancia dentro del borde
            border_progress = (distance - inner_limit) / (outer_limit - inner_limit)
            force_intensity = border_progress * 2.0

        # Vector de fuerza hacia el centro
        return (-dx * force_intensity, -dy * force_intensity)

    def handle_predator_reproduction(self):
        """Gestiona la reproducción y muerte natural de los depredadores."""
        # Probabilidad base de reproducción
        reproduction_chance = 0.0005  # 0.05% por tick

        # Aumentar probabilidad si hay pocos depredadores
        if len(self.predators) < 5:
            reproduction_chance *= 2

        # Limitar número máximo de depredadores
        if len(self.predators) >= self.config.MAX_PREDATORS * 2:
            return

        # Añadir atributo de edad y salud a los depredadores si no lo tienen
        for predator in self.predators:
            if not hasattr(predator, "age"):
                predator.age = 0
                predator.max_age = random.randint(1000, 2000)  # Vida útil del depredador

            if not hasattr(predator, "health"):
                predator.health = 100

            # Añadir referencia al mundo
            predator.world = self

        # Procesar cada depredador (reproducción, envejecimiento, muerte)
        for predator in list(self.predators):  # Usar copia para poder eliminar durante la iteración
            # Envejecer
            predator.age += 1

            # Verificar muerte por vejez o salud
            if predator.age > predator.max_age or predator.health <= 0:
                # Llamar al método die del depredador
                predator.die()
                continue

            # Intentar reproducción
            if random.random() < reproduction_chance:
                # Crear nuevo depredador cerca del progenitor
                offspring_position = (
                    predator.position[0] + random.uniform(-50, 50),
                    predator.position[1] + random.uniform(-50, 50)
                )

                # Heredar características con ligeras variaciones
                offspring_speed = max(0.5, min(2.0, predator.speed + random.uniform(-0.2, 0.2)))

                # Crear nuevo depredador
                offspring = Predator(offspring_position, offspring_speed)
                offspring.health = max(30, min(100, predator.health + random.randint(-10, 10)))
                offspring.damage = max(3, min(20, getattr(predator, "damage", 10) + random.randint(-2, 2)))
                offspring.size = max(15, min(30, predator.size + random.randint(-3, 3)))
                offspring.age = 0
                offspring.max_age = random.randint(1000, 2000)
                offspring.world = self

                # Añadir al mundo
                self.predators.append(offspring)

    def spawn_resource(self):
        """Genera un nuevo recurso en el mundo."""
        x = random.randint(0, self.config.WORLD_SIZE[0])
        y = random.randint(0, self.config.WORLD_SIZE[1])
        resource_type = random.choice(["food", "water", "wood", "stone"])
        value = random.randint(5, 15)

        self.resources.append(Resource(resource_type, (x, y), value))

    def change_weather(self):
        """Cambia el clima del mundo con un sistema más realista."""
        # Actualizar estación si es necesario
        self.season_day += 1
        if self.season_day >= self.season_length:
            self.season_day = 0
            # Cambiar a la siguiente estación
            seasons = ["spring", "summer", "autumn", "winter"]
            current_index = seasons.index(self.season)
            self.season = seasons[(current_index + 1) % len(seasons)]

            # Anunciar cambio de estación a todos los Nexus
            for entity in self.entities:
                if entity.alive:
                    entity.add_memory(f"Comenzó la estación de {self.season}")

        # Determinar si cambia el clima basado en la estación actual
        if random.random() < self.config.WEATHER_CHANGE_RATE * 2:
            # Seleccionar clima basado en probabilidades de la estación
            weather_options = self.seasonal_weather[self.season]
            weather_choices = list(weather_options.keys())
            weather_probabilities = list(weather_options.values())

            self.weather = random.choices(weather_choices, weights=weather_probabilities, k=1)[0]
            self.weather_intensity = random.uniform(0.3, 1.0)
            self.weather_duration = random.randint(100, 500)  # Duración en ticks

            # Notificar a los Nexus del cambio climático
            weather_names = {
                "clear": "despejado",
                "rain": "lluvia",
                "fog": "niebla",
                "wind": "viento",
                "snow": "nieve",
                "blizzard": "ventisca",
                "heat_wave": "ola de calor"
            }

            intensity_names = {
                0.3: "leve",
                0.6: "moderada",
                0.9: "intensa"
            }

            # Encontrar la intensidad más cercana
            closest_intensity = min(intensity_names.keys(), key=lambda x: abs(x - self.weather_intensity))
            intensity_name = intensity_names[closest_intensity]

            for entity in self.entities:
                if entity.alive and entity.attributes["intelligence"] > 0.5:
                    entity.add_memory(f"El clima cambió a {weather_names[self.weather]} {intensity_name}")
        else:
            # Reducir duración del clima actual
            self.weather_duration = max(0, self.weather_duration - 1)
            if self.weather_duration == 0:
                # Volver a clima despejado cuando termina
                self.weather = "clear"
                self.weather_intensity = 0.0

        # Aplicar efectos del clima a los Nexus
        self.apply_weather_effects()

    def apply_weather_effects(self):
        """Aplica los efectos del clima actual a los Nexus."""
        if self.weather == "clear":
            return  # No hay efectos en clima despejado

        # Obtener los efectos del clima actual
        effects = self.weather_effects[self.weather]

        # Aplicar efectos a cada Nexus vivo
        for entity in self.entities:
            if not entity.alive:
                continue

            # Calcular factor de protección basado en conocimientos
            protection = 0.0

            # Conocimientos que protegen contra el clima
            weather_protections = {
                "Construir refugio": 0.7,  # Protege contra la mayoría de climas
                "Hacer fuego": 0.8,      # Protege contra frío
                "Tejer ropa": 0.5,       # Protección básica
                "Construir iglú": 0.9,   # Protección contra nieve/ventisca
                "Cavar madriguera": 0.6  # Protección contra viento/lluvia
            }

            # Verificar conocimientos de protección
            for knowledge, protection_value in weather_protections.items():
                if knowledge in entity.knowledge:
                    protection = max(protection, protection_value)

            # Aplicar efectos reducidos por la protección
            intensity_factor = self.weather_intensity * (1.0 - protection)

            # Aplicar efectos en energía, velocidad y salud
            entity.energy += effects["energy"] * intensity_factor
            entity.health += effects["health"] * intensity_factor

            # Modificar velocidad temporalmente
            if hasattr(entity, "velocity") and effects["speed"] != 0:
                speed_factor = 1.0 + effects["speed"] * intensity_factor
                entity.velocity[0] *= speed_factor
                entity.velocity[1] *= speed_factor

            # Posibilidad de aprender sobre el clima
            if (random.random() < 0.01 * entity.attributes["intelligence"] and
                    entity.emotions["fear"] > 0.5):

                # Conocimientos que pueden desarrollar según el clima
                weather_knowledge = {
                    "rain": "Construir refugio",
                    "snow": "Hacer fuego",
                    "blizzard": "Construir iglú",
                    "wind": "Cavar madriguera",
                    "heat_wave": "Buscar sombra"
                }

                if self.weather in weather_knowledge:
                    knowledge = weather_knowledge[self.weather]
                    if knowledge not in entity.knowledge:
                        entity.learn(knowledge)
                        entity.add_memory(f"Descubrió cómo {knowledge.lower()} durante {self.weather}")
                        entity.emotions["happiness"] = min(1.0, entity.emotions["happiness"] + 0.3)

            # Añadir recuerdos sobre el clima si es intenso
            if intensity_factor > 0.5 and random.random() < 0.1:
                weather_names = {
                    "rain": "la lluvia",
                    "fog": "la niebla",
                    "wind": "el viento",
                    "snow": "la nieve",
                    "blizzard": "la ventisca",
                    "heat_wave": "la ola de calor"
                }

                if self.weather in weather_names:
                    if protection > 0.5:
                        entity.add_memory(f"Se protegió de {weather_names[self.weather]}")
                    else:
                        entity.add_memory(f"Sufrió durante {weather_names[self.weather]}")
                        entity.emotions["fear"] = min(1.0, entity.emotions["fear"] + 0.2)

    def update_camera(self):
        """Actualiza la posición de la cámara."""
        if self.camera_target and self.camera_target.alive and not self.camera_drag:
            # Calcular posición objetivo de la cámara considerando el zoom
            target_x = self.camera_target.position[0] - (self.config.SCREEN_WIDTH / self.camera_zoom) // 2
            target_y = self.camera_target.position[1] - (self.config.SCREEN_HEIGHT / self.camera_zoom) // 2

            # Suavizar movimiento de cámara
            self.camera_offset[0] += (target_x - self.camera_offset[0]) * 0.1
            self.camera_offset[1] += (target_y - self.camera_offset[1]) * 0.1

        # Mantener dentro de los límites del mundo considerando el zoom
        max_offset_x = max(0, self.config.WORLD_SIZE[0] - self.config.SCREEN_WIDTH / self.camera_zoom)
        max_offset_y = max(0, self.config.WORLD_SIZE[1] - self.config.SCREEN_HEIGHT / self.camera_zoom)

        self.camera_offset[0] = max(0, min(max_offset_x, self.camera_offset[0]))
        self.camera_offset[1] = max(0, min(max_offset_y, self.camera_offset[1]))

    def toggle_real_time(self):
        """Activa o desactiva el modo de tiempo real."""
        self.real_time_enabled = not self.real_time_enabled

        # Reiniciar tiempo de inicio si se activa
        if self.real_time_enabled:
            self.real_time_start = pygame.time.get_ticks()
            self.real_time_elapsed = 0

        return self.real_time_enabled

    def set_real_time_factor(self, factor):
        """Establece el factor de tiempo real.

        Args:
            factor (float): Factor de tiempo (1.0 = normal, 2.0 = doble velocidad, etc.)
        """
        # Limitar el factor entre 0.5 y 5.0
        self.real_time_factor = max(0.5, min(5.0, factor))
        return self.real_time_factor

    def toggle_pause(self):
        """Pausa o reanuda la simulación."""
        self.real_time_paused = not self.real_time_paused

        # Reiniciar tiempo de inicio si se reanuda
        if not self.real_time_paused and self.real_time_enabled:
            self.real_time_start = pygame.time.get_ticks()

        return self.real_time_paused

    def set_camera_target(self, entity):
        """Establece el objetivo de la cámara.

        Args:
            entity: Entidad a seguir con la cámara.
        """
        self.camera_target = entity

    def handle_camera_zoom(self, zoom_in):
        """Maneja el zoom de la cámara.

        Args:
            zoom_in (bool): True para acercar, False para alejar.
        """
        # Ajustar factor de zoom
        zoom_factor = 1.1  # Factor de cambio de zoom

        if zoom_in:
            self.camera_zoom *= zoom_factor
        else:
            self.camera_zoom /= zoom_factor

        # Limitar zoom entre 0.5 y 3.0
        self.camera_zoom = max(0.5, min(3.0, self.camera_zoom))
        old_zoom = self.camera_zoom

        # Ajustar factor de zoom
        if zoom_in:
            self.camera_zoom = min(4.0, self.camera_zoom * 1.1)  # Limitar zoom máximo
        else:
            self.camera_zoom = max(0.5, self.camera_zoom / 1.1)  # Limitar zoom mínimo

        # Ajustar offset para mantener el centro de la vista
        center_x = self.camera_offset[0] + (self.config.SCREEN_WIDTH / old_zoom) / 2
        center_y = self.camera_offset[1] + (self.config.SCREEN_HEIGHT / old_zoom) / 2

        # Calcular nuevos offsets para mantener el centro
        self.camera_offset[0] = center_x - (self.config.SCREEN_WIDTH / self.camera_zoom) / 2
        self.camera_offset[1] = center_y - (self.config.SCREEN_HEIGHT / self.camera_zoom) / 2

    def start_camera_drag(self, screen_pos):
        """Inicia el arrastre de la cámara.

        Args:
            screen_pos (tuple): Posición del ratón en la pantalla (x, y).
        """
        self.camera_drag = True
        self.camera_drag_start = list(screen_pos)

    def update_camera_drag(self, screen_pos):
        """Actualiza la posición de la cámara durante el arrastre.

        Args:
            screen_pos (tuple): Posición actual del ratón en la pantalla (x, y).
        """
        if self.camera_drag:
            # Calcular desplazamiento
            dx = (screen_pos[0] - self.camera_drag_start[0]) / self.camera_zoom
            dy = (screen_pos[1] - self.camera_drag_start[1]) / self.camera_zoom

            # Actualizar offset de cámara
            self.camera_offset[0] -= dx
            self.camera_offset[1] -= dy

            # Actualizar posición inicial para el próximo movimiento
            self.camera_drag_start = list(screen_pos)

    def stop_camera_drag(self):
        """Detiene el arrastre de la cámara."""
        self.camera_drag = False

    def get_nearby_predators(self, position, radius=200, exclude=None):
        """Encuentra depredadores cercanos a una posición dentro de un radio.

        Args:
            position (tuple): Posición de referencia (x, y).
            radius (float, optional): Radio de búsqueda. Por defecto es 200.
            exclude (Predator, optional): Depredador a excluir de la búsqueda.

        Returns:
            list: Lista de depredadores dentro del radio especificado.
        """
        nearby = []

        for predator in self.predators:
            if predator == exclude:
                continue

            distance = math.sqrt((position[0] - predator.position[0])**2 +
                                (position[1] - predator.position[1])**2)

            if distance <= radius:
                nearby.append(predator)

        return nearby

    def find_potential_mate(self, nexus):
        """Encuentra un posible compañero para reproducción.

        Args:
            nexus (Nexus): Nexus que busca pareja.

        Returns:
            Nexus: Posible pareja o None si no se encuentra.
        """
        # Buscar Nexus cercanos
        nearby_nexus = self.get_nearby_nexus(nexus.position, 150, exclude=nexus)

        # Filtrar por madurez sexual y energía
        eligible_mates = []
        for other in nearby_nexus:
            if (other.alive and
                other.age >= other.sexual_maturity_age and
                other.energy > self.config.NEXUS_REPRODUCTION_ENERGY_THRESHOLD * 0.8):

                # Verificar compatibilidad
                compatibility = nexus.calculate_compatibility(other)
                if compatibility > 0.5:  # Umbral de compatibilidad
                    eligible_mates.append((other, compatibility))

        # Ordenar por compatibilidad
        eligible_mates.sort(key=lambda x: x[1], reverse=True)

        # Devolver el más compatible o None si no hay candidatos
        return eligible_mates[0][0] if eligible_mates else None

    def get_distance(self, pos1, pos2):
        """Calcula la distancia entre dos posiciones.

        Args:
            pos1 (tuple): Primera posición (x, y).
            pos2 (tuple): Segunda posición (x, y).

        Returns:
            float: Distancia entre las posiciones.
        """
        return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

    def find_nearest_nexus(self, position):
        """Encuentra el Nexus más cercano a una posición.

        Args:
            position (tuple): Posición de referencia (x, y).

        Returns:
            Nexus: Nexus más cercano o None si no hay ninguno vivo.
        """
        nearest = None
        min_distance = float('inf')

        for nexus in self.entities:
            if not nexus.alive:
                continue

            distance = self.get_distance(position, nexus.position)

            if distance < min_distance:
                min_distance = distance
                nearest = nexus

        return nearest

    def get_nearby_nexus(self, position, radius, exclude=None):
        """Encuentra Nexus cercanos a una posición dentro de un radio.

        Args:
            position (tuple): Posición de referencia (x, y).
            radius (float): Radio de búsqueda.
            exclude (Nexus, optional): Nexus a excluir de la búsqueda.

        Returns:
            list: Lista de Nexus dentro del radio especificado.
        """
        nearby = []

        for entity in self.entities:
            if not entity.alive or entity == exclude:
                continue

            distance = math.sqrt((position[0] - entity.position[0])**2 +
                                (position[1] - entity.position[1])**2)

            if distance <= radius:
                nearby.append(entity)

        return nearby

    def find_nearest_resource(self, position, resource_type=None):
        """Encuentra el recurso más cercano a una posición.

        Args:
            position (tuple): Posición de referencia (x, y).
            resource_type (str, optional): Tipo de recurso a buscar.

        Returns:
            Resource: Recurso más cercano o None si no hay ninguno.
        """
        nearest = None
        min_distance = float('inf')

        for resource in self.resources:
            if resource_type and resource.type != resource_type:
                continue

            distance = math.sqrt((position[0] - resource.position[0])**2 +
                                (position[1] - resource.position[1])**2)

            if distance < min_distance:
                min_distance = distance
                nearest = resource

        return nearest

    def find_nearest_nexus(self, position, exclude=None):
        """Encuentra el Nexus más cercano a una posición.

        Args:
            position (tuple): Posición de referencia (x, y).
            exclude (Nexus, optional): Nexus a excluir de la búsqueda.

        Returns:
            Nexus: Nexus más cercano o None si no hay ninguno.
        """
        nearest = None
        min_distance = float('inf')

        for entity in self.entities:
            if not entity.alive or entity == exclude:
                continue

            distance = math.sqrt((position[0] - entity.position[0])**2 +
                                (position[1] - entity.position[1])**2)

            if distance < min_distance:
                min_distance = distance
                nearest = entity

        return nearest



    def find_potential_mate(self, nexus):
        """Encuentra una pareja potencial para un Nexus.

        Args:
            nexus (Nexus): Nexus que busca pareja.

        Returns:
            Nexus: Pareja potencial o None si no hay ninguna adecuada.
        """
        potential_mates = []

        for entity in self.entities:
            if (entity != nexus and entity.alive and
                    entity.age > 1000 and entity.energy > 70):  # Madurez y energía

                # Calcular compatibilidad
                compatibility = nexus.calculate_compatibility(entity)

                if compatibility > 0.6:  # Umbral de compatibilidad
                    potential_mates.append((entity, compatibility))

        if potential_mates:
            # Ordenar por compatibilidad
            potential_mates.sort(key=lambda x: x[1], reverse=True)
            return potential_mates[0][0]  # Devolver el más compatible

        return None

    def get_resource_at_position(self, position, radius=20):
        """Obtiene un recurso en una posición específica.

        Args:
            position (tuple): Posición a comprobar (x, y).
            radius (int): Radio de detección.

        Returns:
            Resource: Recurso encontrado o None.
        """
        for resource in self.resources:
            distance = math.sqrt((position[0] - resource.position[0])**2 +
                                (position[1] - resource.position[1])**2)

            if distance < radius:
                return resource

        return None

    def find_resource_at_position(self, position, radius=20):
        """Encuentra un recurso en una posición específica.

        Args:
            position (tuple): Posición a comprobar (x, y).
            radius (int): Radio de detección.

        Returns:
            Resource: Recurso encontrado o None.
        """
        # Este método es un alias de get_resource_at_position para mantener compatibilidad
        return self.get_resource_at_position(position, radius)

    def get_nexus_at_position(self, position, radius=30):
        """Obtiene un Nexus en una posición específica.

        Args:
            position (tuple): Posición a comprobar (x, y).
            radius (int): Radio de detección.

        Returns:
            Nexus: Nexus encontrado o None.
        """
        for entity in self.entities:
            if not entity.alive:
                continue

            distance = math.sqrt((position[0] - entity.position[0])**2 +
                                (position[1] - entity.position[1])**2)

            if distance < radius:
                return entity

        return None

    def find_nexus_at_position(self, position, radius=30):
        """Encuentra un Nexus en una posición específica.

        Args:
            position (tuple): Posición a comprobar (x, y).
            radius (int): Radio de detección.

        Returns:
            Nexus: Nexus encontrado o None.
        """
        # Este método es un alias de get_nexus_at_position para mantener compatibilidad
        return self.get_nexus_at_position(position, radius)



    def get_nearest_predator(self, position):
        """Obtiene el depredador más cercano a una posición.

        Args:
            position (tuple): Posición de referencia (x, y).

        Returns:
            Predator: Depredador más cercano o None.
        """
        nearest = None
        min_distance = float('inf')

        for predator in self.predators:
            distance = math.sqrt((position[0] - predator.position[0])**2 +
                                (position[1] - predator.position[1])**2)

            if distance < min_distance:
                min_distance = distance
                nearest = predator

        return nearest

    def get_predator_at_position(self, position, radius=30):
        """Obtiene un depredador en una posición específica.

        Args:
            position (tuple): Posición a comprobar (x, y).
            radius (int): Radio de detección.

        Returns:
            Predator: Depredador encontrado o None.
        """
        for predator in self.predators:
            distance = math.sqrt((position[0] - predator.position[0])**2 +
                                (position[1] - predator.position[1])**2)

            if distance < radius:
                return predator

        return None

    def get_discoverable_knowledge(self, nexus):
        """Obtiene un conocimiento que el Nexus puede descubrir.

        Args:
            nexus (Nexus): Nexus que intenta descubrir.

        Returns:
            str: Conocimiento descubierto o None.
        """
        # Filtrar conocimientos que el Nexus aún no tiene
        unknown_knowledge = [k for k in self.available_knowledge if k not in nexus.knowledge]

        if unknown_knowledge:
            # Probabilidad basada en inteligencia
            if random.random() < nexus.attributes["intelligence"] * 0.2:
                return random.choice(unknown_knowledge)

        return None

    def render(self, screen):
        """Renderiza el mundo en la pantalla con un terreno más realista.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
        """
        # Renderizar terreno simple
        import pygame

        # Dibujar fondo (océano)
        screen.fill((50, 100, 150))  # Azul océano

        # Calcular centro de la pantalla
        center_screen_x = screen.get_width() // 2
        center_screen_y = screen.get_height() // 2

        # Radio del mundo circular
        world_radius_px = min(screen.get_width(), screen.get_height()) // 2 - 50

        # Aplicar transformación de cámara al centro del mundo
        transformed_center_x = center_screen_x - self.camera_offset[0] * self.camera_zoom
        transformed_center_y = center_screen_y - self.camera_offset[1] * self.camera_zoom

        # Dibujar borde circular (playa)
        beach_radius = world_radius_px * self.camera_zoom

        # Dibujar círculo base de playa
        pygame.draw.circle(
            screen,
            (220, 210, 160),  # Color arena
            (int(transformed_center_x), int(transformed_center_y)),
            int(beach_radius)
        )

        # Dibujar tierra firme (interior del círculo)
        land_radius = int(world_radius_px * 0.95 * self.camera_zoom)

        # Dibujar círculo base de tierra
        pygame.draw.circle(
            screen,
            (50, 120, 80),  # Verde bosque
            (int(transformed_center_x), int(transformed_center_y)),
            land_radius
        )

        # Renderizar recursos
        for resource in self.resources:
            resource.render(screen, self.camera_offset)

        # Renderizar refugios
        self.shelter_system.render(screen, self.camera_offset, self.camera_zoom)

        # Renderizar cadáveres en descomposición
        self.decomposition_system.render(screen, self.camera_offset, self.camera_zoom)

        # Renderizar depredadores
        for predator in self.predators:
            predator.render(screen, self.camera_offset)

        # Renderizar entidades (Nexus)
        for entity in self.entities:
            if entity.alive:
                # Usar el renderizador avanzado si está disponible
                if hasattr(self, "nexus_renderer"):
                    self.nexus_renderer.render(entity, screen, self.camera_offset, self.camera_zoom)
                else:
                    # Fallback al método de renderizado antiguo
                    entity.render(screen, self.camera_offset)

        # Información de la era tecnológica (HUD)
        if hasattr(self, "technology_system") and hasattr(self, "font"):
            self.technology_system.render_era_info(screen, self.font, 10, 10)

        # Eventos importantes
        if hasattr(self, "events") and hasattr(self, "font"):
            self.render_events(screen, self.font)

    def _render_realistic_terrain(self, screen):
        """Renderiza un terreno más realista con variedad de elementos naturales.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
        """
        import pygame
        import random
        import math
        import numpy as np
        from pygame import gfxdraw

        # Determinar color base según estación y clima
        base_color = (50, 120, 80)  # Verde bosque (por defecto)

        # Ajustar color base según estación
        if self.season == "spring":
            base_color = (70, 150, 70)  # Verde más brillante
        elif self.season == "summer":
            base_color = (50, 120, 80)  # Verde bosque
        elif self.season == "autumn":
            base_color = (150, 100, 50)  # Marrón anaranjado
        elif self.season == "winter":
            base_color = (200, 200, 220)  # Blanco azulado

        # Ajustar según clima
        if self.weather == "clear":
            terrain_color = base_color
        elif self.weather == "rain":
            # Oscurecer el color base
            terrain_color = (max(0, base_color[0] - 30),
                          max(0, base_color[1] - 30),
                          min(255, base_color[2] + 20))
        elif self.weather == "fog":
            # Mezclar con gris
            terrain_color = ((base_color[0] + 200) // 2,
                          (base_color[1] + 200) // 2,
                          (base_color[2] + 200) // 2)
        elif self.weather == "snow":
            terrain_color = (230, 230, 240)  # Blanco azulado
        elif self.weather == "heat_wave":
            # Más amarillento
            terrain_color = (min(255, base_color[0] + 50),
                          min(255, base_color[1] + 20),
                          max(0, base_color[2] - 30))
        else:
            terrain_color = base_color

        # Llenar la pantalla con un color de fondo (cielo)
        sky_color = (100, 150, 200)  # Azul cielo
        if self.weather == "rain" or self.weather == "fog":
            sky_color = (80, 100, 120)  # Cielo nublado
        elif self.weather == "snow":
            sky_color = (200, 210, 230)  # Cielo invernal
        elif self.weather == "heat_wave":
            sky_color = (150, 180, 220)  # Cielo caluroso

        screen.fill(sky_color)

        # Dibujar océano (exterior del mundo)
        ocean_color = (0, 80, 150)  # Azul oscuro para el océano

        # Calcular centro del mundo en coordenadas de pantalla
        center_screen_x = int((self.center_x - self.camera_offset[0]) * self.camera_zoom)
        center_screen_y = int((self.center_y - self.camera_offset[1]) * self.camera_zoom)
        world_radius_px = int(self.world_radius * self.camera_zoom)

        # Dibujar océano con efecto de ondas
        ocean_surface = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)

        # Crear gradiente de océano (más oscuro en el exterior)
        for r in range(world_radius_px, world_radius_px + 50, 2):
            alpha = 255 - min(255, (r - world_radius_px) * 5)
            wave_offset = math.sin(self.time / 20 + r / 10) * 2
            pygame.draw.circle(
                ocean_surface,
                (*ocean_color, alpha),
                (center_screen_x, center_screen_y),
                r + wave_offset,
                3
            )

        # Dibujar océano principal
        pygame.draw.circle(
            ocean_surface,
            (*ocean_color, 220),
            (center_screen_x, center_screen_y),
            world_radius_px
        )

        # Aplicar superficie del océano
        screen.blit(ocean_surface, (0, 0))

        # Dibujar playa (borde entre océano y tierra)
        beach_color = (240, 220, 180)  # Color arena
        beach_width = int(world_radius_px * 0.04)
        beach_radius = int(world_radius_px * 0.98)

        # Dibujar playa con textura
        beach_surface = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)
        pygame.draw.circle(
            beach_surface,
            (*beach_color, 230),
            (center_screen_x, center_screen_y),
            beach_radius,
            beach_width
        )

        # Añadir textura a la playa (puntos aleatorios)
        for _ in range(beach_width * 100):
            # Generar ángulo y radio aleatorios dentro del anillo de la playa
            angle = random.uniform(0, 2 * math.pi)
            radius = random.uniform(beach_radius - beach_width, beach_radius)

            # Calcular coordenadas
            x = center_screen_x + int(radius * math.cos(angle))
            y = center_screen_y + int(radius * math.sin(angle))

            # Dibujar punto con color ligeramente variado
            color_var = random.randint(-20, 20)
            dot_color = (
                min(255, max(0, beach_color[0] + color_var)),
                min(255, max(0, beach_color[1] + color_var)),
                min(255, max(0, beach_color[2] + color_var)),
                150
            )

            # Dibujar punto si está dentro de la pantalla
            if 0 <= x < screen.get_width() and 0 <= y < screen.get_height():
                pygame.gfxdraw.pixel(beach_surface, x, y, dot_color)

        # Aplicar superficie de la playa
        screen.blit(beach_surface, (0, 0))

        # Dibujar tierra firme (interior del círculo)
        land_radius = int(world_radius_px * 0.95)

        # Crear superficie para la tierra con textura
        land_surface = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)

        # Dibujar círculo base de tierra
        pygame.draw.circle(
            land_surface,
            (*terrain_color, 255),
            (center_screen_x, center_screen_y),
            land_radius
        )

        # Añadir textura al terreno (variaciones de color)
        # Usar ruido Perlin para generar patrones naturales
        if not hasattr(self, 'terrain_seed'):
            self.terrain_seed = random.randint(0, 1000)

        # Simplificar con patrones de textura aleatorios
        for i in range(1000):
            # Generar posición aleatoria dentro del círculo de tierra
            angle = random.uniform(0, 2 * math.pi)
            radius = random.uniform(0, land_radius * 0.95)  # Evitar el borde

            x = center_screen_x + int(radius * math.cos(angle))
            y = center_screen_y + int(radius * math.sin(angle))

            # Verificar si está dentro de la pantalla
            if x < 0 or x >= screen.get_width() or y < 0 or y >= screen.get_height():
                continue

            # Determinar tipo de textura (hierba, tierra, rocas)
            texture_type = random.choices(
                ["grass", "dirt", "rock", "moss"],
                weights=[0.7, 0.2, 0.05, 0.05],
                k=1
            )[0]

            # Color base según tipo
            if texture_type == "grass":
                color_base = (
                    max(0, terrain_color[0] - 10),
                    min(255, terrain_color[1] + 20),
                    max(0, terrain_color[2] - 10)
                )
                size = random.randint(3, 8)
            elif texture_type == "dirt":
                color_base = (
                    min(255, terrain_color[0] + 20),
                    max(0, terrain_color[1] - 20),
                    max(0, terrain_color[2] - 30)
                )
                size = random.randint(5, 12)
            elif texture_type == "rock":
                gray = random.randint(100, 150)
                color_base = (gray, gray, gray)
                size = random.randint(4, 10)
            else:  # moss
                color_base = (
                    max(0, terrain_color[0] - 30),
                    min(255, terrain_color[1] + 10),
                    max(0, terrain_color[2] - 20)
                )
                size = random.randint(3, 7)

            # Añadir variación al color
            color_var = random.randint(-15, 15)
            color = (
                min(255, max(0, color_base[0] + color_var)),
                min(255, max(0, color_base[1] + color_var)),
                min(255, max(0, color_base[2] + color_var)),
                random.randint(100, 200)  # Transparencia variable
            )

            # Dibujar elemento de textura
            if texture_type in ["grass", "moss"]:
                # Dibujar hierba como líneas verticales
                line_height = random.randint(2, size)
                pygame.draw.line(
                    land_surface,
                    color,
                    (x, y),
                    (x, y - line_height),
                    1
                )
            elif texture_type == "dirt":
                # Dibujar tierra como pequeños círculos
                pygame.draw.circle(
                    land_surface,
                    color,
                    (x, y),
                    random.randint(1, 2)
                )
            else:  # rock
                # Dibujar rocas como polígonos irregulares
                points = []
                for j in range(random.randint(3, 6)):
                    angle_j = j * 2 * math.pi / random.randint(3, 6)
                    dist = random.uniform(size * 0.5, size)
                    points.append((
                        x + int(dist * math.cos(angle_j)),
                        y + int(dist * math.sin(angle_j))
                    ))
                pygame.draw.polygon(land_surface, color, points)

        # Aplicar superficie de tierra
        screen.blit(land_surface, (0, 0))

        # Efectos de clima
        if self.weather == "rain":
            # Dibujar gotas de lluvia
            rain_surface = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)
            for _ in range(300):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height())
                length = random.randint(5, 15)
                pygame.draw.line(
                    rain_surface,
                    (200, 200, 255, 150),
                    (x, y),
                    (x - 1, y + length),
                    1
                )
            screen.blit(rain_surface, (0, 0))

        elif self.weather == "fog":
            # Dibujar niebla como capas semitransparentes
            fog_surface = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)
            fog_surface.fill((255, 255, 255, 100))
            screen.blit(fog_surface, (0, 0))

            # Añadir parches de niebla más densos
            for _ in range(20):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height())
                size = random.randint(50, 200)
                fog_patch = pygame.Surface((size, size), pygame.SRCALPHA)
                pygame.draw.circle(
                    fog_patch,
                    (255, 255, 255, random.randint(50, 150)),
                    (size // 2, size // 2),
                    size // 2
                )
                screen.blit(fog_patch, (x - size // 2, y - size // 2))

        elif self.weather == "snow":
            # Dibujar copos de nieve
            for _ in range(200):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height())
                size = random.randint(1, 3)
                pygame.draw.circle(
                    screen,
                    (255, 255, 255, random.randint(150, 255)),
                    (x, y),
                    size
                )

        elif self.weather == "heat_wave":
            # Efecto de distorsión por calor
            heat_surface = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)
            for _ in range(50):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height() - 50)
                width = random.randint(30, 100)
                height = random.randint(10, 30)

                # Dibujar ondas de calor semitransparentes
                for i in range(height):
                    alpha = 150 - i * 5
                    if alpha > 0:
                        wave_x = x + int(math.sin(self.time / 10 + i / 5) * 5)
                        pygame.draw.line(
                            heat_surface,
                            (255, 255, 255, alpha),
                            (wave_x, y + i),
                            (wave_x + width, y + i),
                            1
                        )

            screen.blit(heat_surface, (0, 0))

        # Renderizar elementos naturales en orden de profundidad

        # 1. Cuerpos de agua (fondo)
        for water_body in self.water_bodies:
            water_body.render(screen, self.camera_offset, self.camera_zoom)

        # 2. Recursos
        for resource in self.resources:
            resource.render(screen, self.camera_offset)

        # 3. Plantas
        for plant in self.plants:
            plant.render(screen, self.camera_offset, self.camera_zoom)

        # 4. Animales
        for animal in self.animals:
            animal.render(screen, self.camera_offset, self.camera_zoom)

        # 5. Árboles
        for tree in self.trees:
            tree.render(screen, self.camera_offset, self.camera_zoom)

        # Renderizar efectos del clima
        if self.weather == "rainy":
            for _ in range(100):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height())
                pygame.draw.line(screen, (200, 200, 255), (x, y), (x, y + 10), 1)

        elif self.weather == "stormy":
            for _ in range(50):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height())
                pygame.draw.line(screen, (200, 200, 255), (x, y), (x + 5, y + 15), 2)

            # Ocasionalmente dibujar relámpagos
            if random.random() < 0.05:
                start_x = random.randint(0, screen.get_width())
                pygame.draw.line(screen, (255, 255, 200),
                               (start_x, 0),
                               (start_x + random.randint(-100, 100), screen.get_height() // 2),
                               3)

        elif self.weather == "foggy":
            fog = pygame.Surface((screen.get_width(), screen.get_height()), pygame.SRCALPHA)
            fog.fill((255, 255, 255, 100))  # Blanco semi-transparente
            screen.blit(fog, (0, 0))

        elif self.weather == "snowy":
            for _ in range(200):
                x = random.randint(0, screen.get_width())
                y = random.randint(0, screen.get_height())
                pygame.draw.circle(screen, (255, 255, 255), (x, y), 2)

    def generate_initial_resources(self):
        """Genera los recursos iniciales en el mundo."""
        # Generar comida
        for _ in range(self.config.MAX_RESOURCES // 2):
            x = random.randint(0, self.config.WORLD_SIZE[0])
            y = random.randint(0, self.config.WORLD_SIZE[1])
            resource_type = random.choice(["food", "water", "wood", "stone"])
            value = random.randint(5, 15)

            self.resources.append(Resource(resource_type, (x, y), value))

    def generate_predators(self):
        """Genera los depredadores iniciales en el mundo."""
        # Reducir la cantidad inicial de depredadores
        num_predators = max(1, self.config.MAX_PREDATORS // 2)  # La mitad de los configurados, mínimo 1

        for _ in range(num_predators):
            # Colocar depredadores lejos del centro donde comienza el primer Nexus
            margin = 300  # Margen desde el centro
            center_x, center_y = self.config.WORLD_SIZE[0] // 2, self.config.WORLD_SIZE[1] // 2

            # Generar posición alejada del centro
            while True:
                x = random.randint(0, self.config.WORLD_SIZE[0])
                y = random.randint(0, self.config.WORLD_SIZE[1])
                distance_from_center = math.sqrt((x - center_x)**2 + (y - center_y)**2)
                if distance_from_center > margin:
                    break

            speed = random.uniform(0.8, 1.5)  # Velocidad reducida

            self.predators.append(Predator((x, y), speed))

    def add_entity(self, entity):
        """Añade una entidad al mundo.

        Args:
            entity: Entidad a añadir (Nexus).
        """
        # Inicializar Nexus con sistemas avanzados
        self.initialize_nexus(entity)

        # Añadir a la lista de entidades
        self.entities.append(entity)

    def update(self):
        """Actualiza el estado del mundo en cada tick."""
        # Incrementar tiempo según el factor de tiempo real si está activado
        if self.real_time_enabled and not self.real_time_paused:
            current_time = pygame.time.get_ticks()
            if self.real_time_start == 0:
                self.real_time_start = current_time
            else:
                # Calcular tiempo transcurrido desde el último update
                elapsed = current_time - self.real_time_start
                self.real_time_elapsed += elapsed
                # Actualizar tiempo de inicio para el próximo cálculo
                self.real_time_start = current_time

                # Aplicar factor de tiempo real a la edad de los Nexus
                time_increment = max(1, int(self.real_time_factor))
                self.time += time_increment
        else:
            # Incremento normal de tiempo
            self.time += 1

        # Actualizar entidades
        for entity in self.entities:
            if entity.alive:
                # Si el tiempo real está activado, actualizar la edad según el factor
                if self.real_time_enabled and not self.real_time_paused:
                    entity.age += max(0, int(self.real_time_factor - 1))
                entity.update(self)

        # Actualizar depredadores
        for predator in self.predators:
            predator.update(self)
        # Verificar si hay suficientes Nexus vivos, si no, aumentar probabilidad de reproducción
        alive_nexus = [n for n in self.entities if n.alive]
        if len(alive_nexus) < 3:
            # Dar un impulso a la reproducción cuando hay pocos Nexus
            for nexus in alive_nexus:
                if nexus.energy > self.config.NEXUS_REPRODUCTION_ENERGY_THRESHOLD * 0.8:
                    nexus.energy = self.config.NEXUS_MAX_ENERGY * 0.9  # Dar energía extra para reproducción

        # Eliminar recursos consumidos
        self.resources = [r for r in self.resources if not r.consumed]

        # Generar nuevos recursos ocasionalmente
        if (len(self.resources) < self.config.MAX_RESOURCES and
                random.random() < self.config.RESOURCE_RESPAWN_RATE):
            self.spawn_resource()

        # Cambiar clima ocasionalmente
        if random.random() < self.config.WEATHER_CHANGE_RATE:
            self.change_weather()
        else:
            # Aplicar efectos del clima actual
            self.apply_weather_effects()

        # Actualizar sistema de creencias si está inicializado
        if self.belief_system:
            self.belief_system.update()

        # Actualizar cámara si hay un objetivo
        self.update_camera()

    def spawn_resource(self):
        """Genera un nuevo recurso en una posición aleatoria dentro del área verde."""
        # Elegir tipo de recurso
        resource_type = random.choice(["food", "water", "wood", "stone"])

        # Valor aleatorio
        value = random.randint(5, 15)

        # Generar posición aleatoria dentro del área verde (90% del radio)
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(0, self.world_radius * 0.9)  # Solo en el área verde

        x = self.center_x + distance * math.cos(angle)
        y = self.center_y + distance * math.sin(angle)

        # Crear y añadir recurso
        self.resources.append(Resource(resource_type, (x, y), value))

    def change_weather(self):
        """Cambia el clima del mundo con un sistema más realista."""
        # Actualizar estación si es necesario
        self.season_day += 1
        if self.season_day >= self.season_length:
            self.season_day = 0
            # Cambiar a la siguiente estación
            seasons = ["spring", "summer", "autumn", "winter"]
            current_index = seasons.index(self.season)
            self.season = seasons[(current_index + 1) % len(seasons)]

            # Anunciar cambio de estación a todos los Nexus
            for entity in self.entities:
                if entity.alive:
                    entity.add_memory(f"Comenzó la estación de {self.season}")

        # Determinar si cambia el clima basado en la estación actual
        if random.random() < self.config.WEATHER_CHANGE_RATE * 2:
            # Seleccionar clima basado en probabilidades de la estación
            weather_options = self.seasonal_weather[self.season]
            weather_choices = list(weather_options.keys())
            weather_probabilities = list(weather_options.values())

            self.weather = random.choices(weather_choices, weights=weather_probabilities, k=1)[0]
            self.weather_intensity = random.uniform(0.3, 1.0)
            self.weather_duration = random.randint(100, 500)  # Duración en ticks

            # Notificar a los Nexus del cambio climático
            weather_names = {
                "clear": "despejado",
                "rain": "lluvia",
                "fog": "niebla",
                "wind": "viento",
                "snow": "nieve",
                "blizzard": "ventisca",
                "heat_wave": "ola de calor"
            }

            intensity_names = {
                0.3: "leve",
                0.6: "moderada",
                0.9: "intensa"
            }

            # Encontrar la intensidad más cercana
            closest_intensity = min(intensity_names.keys(), key=lambda x: abs(x - self.weather_intensity))
            intensity_name = intensity_names[closest_intensity]

            for entity in self.entities:
                if entity.alive and entity.attributes["intelligence"] > 0.5:
                    entity.add_memory(f"El clima cambió a {weather_names[self.weather]} {intensity_name}")
        else:
            # Reducir duración del clima actual
            self.weather_duration = max(0, self.weather_duration - 1)
            if self.weather_duration == 0:
                # Volver a clima despejado cuando termina
                self.weather = "clear"
                self.weather_intensity = 0.0

        # Aplicar efectos del clima a los Nexus
        self.apply_weather_effects()

    def apply_weather_effects(self):
        """Aplica los efectos del clima actual a los Nexus."""
        if self.weather == "clear":
            return  # No hay efectos en clima despejado

        # Obtener los efectos del clima actual
        effects = self.weather_effects[self.weather]

        # Aplicar efectos a cada Nexus vivo
        for entity in self.entities:
            if not entity.alive:
                continue

            # Calcular factor de protección basado en conocimientos
            protection = 0.0

            # Conocimientos que protegen contra el clima
            weather_protections = {
                "Construir refugio": 0.7,  # Protege contra la mayoría de climas
                "Hacer fuego": 0.8,      # Protege contra frío
                "Tejer ropa": 0.5,       # Protección básica
                "Construir iglú": 0.9,   # Protección contra nieve/ventisca
                "Cavar madriguera": 0.6  # Protección contra viento/lluvia
            }

            # Verificar conocimientos de protección
            for knowledge, protection_value in weather_protections.items():
                if knowledge in entity.knowledge:
                    protection = max(protection, protection_value)

            # Aplicar efectos reducidos por la protección
            intensity_factor = self.weather_intensity * (1.0 - protection)

            # Aplicar efectos en energía, velocidad y salud
            entity.energy += effects["energy"] * intensity_factor
            entity.health += effects["health"] * intensity_factor

            # Modificar velocidad temporalmente
            if hasattr(entity, "velocity") and effects["speed"] != 0:
                speed_factor = 1.0 + effects["speed"] * intensity_factor
                entity.velocity[0] *= speed_factor
                entity.velocity[1] *= speed_factor

            # Posibilidad de aprender sobre el clima
            if (random.random() < 0.01 * entity.attributes["intelligence"] and
                    entity.emotions["fear"] > 0.5):

                # Conocimientos que pueden desarrollar según el clima
                weather_knowledge = {
                    "rain": "Construir refugio",
                    "snow": "Hacer fuego",
                    "blizzard": "Construir iglú",
                    "wind": "Cavar madriguera",
                    "heat_wave": "Buscar sombra"
                }

                if self.weather in weather_knowledge:
                    knowledge = weather_knowledge[self.weather]
                    if knowledge not in entity.knowledge:
                        entity.learn(knowledge)
                        entity.add_memory(f"Descubrió cómo {knowledge.lower()} durante {self.weather}")
                        entity.emotions["happiness"] = min(1.0, entity.emotions["happiness"] + 0.3)

            # Añadir recuerdos sobre el clima si es intenso
            if intensity_factor > 0.5 and random.random() < 0.1:
                weather_names = {
                    "rain": "la lluvia",
                    "fog": "la niebla",
                    "wind": "el viento",
                    "snow": "la nieve",
                    "blizzard": "la ventisca",
                    "heat_wave": "la ola de calor"
                }

                if self.weather in weather_names:
                    if protection > 0.5:
                        entity.add_memory(f"Se protegió de {weather_names[self.weather]}")
                    else:
                        entity.add_memory(f"Sufrió durante {weather_names[self.weather]}")
                        entity.emotions["fear"] = min(1.0, entity.emotions["fear"] + 0.2)

    def update_camera(self):
        """Actualiza la posición de la cámara."""
        if self.camera_target and self.camera_target.alive and not self.camera_drag:
            # Calcular posición objetivo de la cámara considerando el zoom
            target_x = self.camera_target.position[0] - (self.config.SCREEN_WIDTH / self.camera_zoom) // 2
            target_y = self.camera_target.position[1] - (self.config.SCREEN_HEIGHT / self.camera_zoom) // 2

            # Suavizar movimiento de cámara
            self.camera_offset[0] += (target_x - self.camera_offset[0]) * 0.1
            self.camera_offset[1] += (target_y - self.camera_offset[1]) * 0.1

        # Mantener dentro de los límites del mundo considerando el zoom
        max_offset_x = max(0, self.config.WORLD_SIZE[0] - self.config.SCREEN_WIDTH / self.camera_zoom)
        max_offset_y = max(0, self.config.WORLD_SIZE[1] - self.config.SCREEN_HEIGHT / self.camera_zoom)

        self.camera_offset[0] = max(0, min(max_offset_x, self.camera_offset[0]))
        self.camera_offset[1] = max(0, min(max_offset_y, self.camera_offset[1]))

    def toggle_real_time(self):
        """Activa o desactiva el modo de tiempo real."""
        self.real_time_enabled = not self.real_time_enabled

        # Reiniciar tiempo de inicio si se activa
        if self.real_time_enabled:
            self.real_time_start = pygame.time.get_ticks()
            self.real_time_elapsed = 0

        return self.real_time_enabled

    def set_real_time_factor(self, factor):
        """Establece el factor de tiempo real.

        Args:
            factor (float): Factor de tiempo (1.0 = normal, 2.0 = doble velocidad, etc.)
        """
        # Limitar el factor entre 0.5 y 5.0
        self.real_time_factor = max(0.5, min(5.0, factor))
        return self.real_time_factor

    def toggle_pause(self):
        """Pausa o reanuda la simulación."""
        self.real_time_paused = not self.real_time_paused

        # Reiniciar tiempo de inicio si se reanuda
        if not self.real_time_paused and self.real_time_enabled:
            self.real_time_start = pygame.time.get_ticks()

        return self.real_time_paused

    def set_camera_target(self, entity):
        """Establece el objetivo de la cámara.

        Args:
            entity: Entidad a seguir con la cámara.
        """
        self.camera_target = entity

    def start_camera_drag(self, mouse_pos):
        """Inicia el arrastre de la cámara.

        Args:
            mouse_pos (tuple): Posición inicial del ratón.
        """
        self.camera_drag = True
        self.camera_drag_start = list(mouse_pos)
        self.camera_target = None  # Desactivar seguimiento

    def update_camera_drag(self, mouse_pos):
        """Actualiza la posición de la cámara durante el arrastre.

        Args:
            mouse_pos (tuple): Posición actual del ratón.
        """
        if self.camera_drag:
            # Calcular desplazamiento
            dx = mouse_pos[0] - self.camera_drag_start[0]
            dy = mouse_pos[1] - self.camera_drag_start[1]

            # Actualizar posición de la cámara (invertir para que el movimiento sea natural)
            self.camera_offset[0] -= dx / self.camera_zoom
            self.camera_offset[1] -= dy / self.camera_zoom

            # Actualizar posición inicial para el próximo movimiento
            self.camera_drag_start = list(mouse_pos)

    def stop_camera_drag(self):
        """Finaliza el arrastre de la cámara."""
        self.camera_drag = False

    def handle_camera_zoom(self, zoom_in):
        """Maneja el zoom de la cámara.

        Args:
            zoom_in (bool): True para acercar, False para alejar.
        """
        # Ajustar factor de zoom
        zoom_factor = 1.1  # Factor de cambio de zoom

        if zoom_in:
            self.camera_zoom *= zoom_factor
        else:
            self.camera_zoom /= zoom_factor

        # Limitar zoom entre 0.5 y 3.0
        self.camera_zoom = max(0.5, min(3.0, self.camera_zoom))