#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema de estrés y psicología para el simulador Nexus.

Implementa mecánicas de estrés psicológico, comportamientos emergentes
y dinámicas sociales complejas inspiradas en el experimento Universe 25.
"""

import random
import math
from collections import defaultdict

class StressPsychologySystem:
    """Sistema que gestiona el estrés psicológico y comportamientos emergentes."""
    
    def __init__(self, config):
        """Inicializa el sistema de estrés y psicología.
        
        Args:
            config (Config): Configuración del simulador.
        """
        self.config = config
        
        # Factores de estrés
        self.stress_factors = {
            "overcrowding": {
                "threshold": 10,  # Número de individuos en área pequeña
                "radius": 100,    # Radio de detección de hacinamiento
                "intensity": 0.1  # Incremento de estrés por individuo extra
            },
            "resource_competition": {
                "threshold": 5,   # Número de competidores por recurso
                "radius": 50,     # Radio de competencia
                "intensity": 0.15 # Incremento de estrés por competidor
            },
            "social_isolation": {
                "threshold": 1000, # Tiempo sin interacción social
                "intensity": 0.05  # Incremento de estrés por unidad de tiempo
            },
            "territory_invasion": {
                "intensity": 0.2   # Estrés por invasión territorial
            },
            "health_decline": {
                "threshold": 50,   # Salud por debajo de la cual aumenta estrés
                "intensity": 0.1   # Incremento de estrés por punto de salud perdido
            }
        }
        
        # Efectos del estrés en el comportamiento
        self.stress_effects = {
            "low": (0.0, 0.3),      # Estrés bajo: comportamiento normal
            "moderate": (0.3, 0.6), # Estrés moderado: cambios menores
            "high": (0.6, 0.8),     # Estrés alto: comportamientos anómalos
            "critical": (0.8, 1.0)  # Estrés crítico: colapso comportamental
        }
        
        # Comportamientos emergentes por nivel de estrés
        self.stress_behaviors = {
            "moderate": ["withdrawing", "aggressive", "hoarding"],
            "high": ["aggressive", "withdrawing", "stressed", "competing"],
            "critical": ["withdrawing", "aggressive", "submissive", "stressed"]
        }
        
        # Patrones de comportamiento anómalo (Universe 25)
        self.anomalous_behaviors = {
            "beautiful_ones": {  # Individuos que se retiran socialmente
                "traits": {"social": 0.1, "grooming": 0.9, "reproduction": 0.1},
                "description": "Se retiran de la sociedad, se acicalan excesivamente"
            },
            "aggressive_dominants": {  # Hiperdominio agresivo
                "traits": {"aggression": 0.9, "dominance": 0.9, "territory": 0.9},
                "description": "Extremadamente agresivos y territoriales"
            },
            "passive_withdrawn": {  # Pasivos y retraídos
                "traits": {"social": 0.1, "activity": 0.2, "curiosity": 0.1},
                "description": "Pasivos, evitan interacciones y actividades"
            },
            "hyperactive_wanderers": {  # Hiperactividad sin propósito
                "traits": {"activity": 0.9, "exploration": 0.9, "focus": 0.1},
                "description": "Hiperactividad sin propósito definido"
            }
        }
        
        # Registro de comportamientos anómalos por individuo
        self.anomalous_individuals = {}  # {nexus_id: behavior_type}
        
        # Estadísticas poblacionales
        self.population_stats = {
            "total_population": 0,
            "stress_levels": defaultdict(int),
            "anomalous_count": defaultdict(int),
            "social_breakdown": 0.0,
            "reproduction_rate": 0.0,
            "mortality_rate": 0.0
        }
    
    def update(self, world):
        """Actualiza el sistema de estrés para todos los Nexus.
        
        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Actualizar estadísticas poblacionales
        self._update_population_stats(world)
        
        # Calcular factores de estrés globales
        global_stress = self._calculate_global_stress(world)
        
        # Actualizar estrés individual
        for nexus in world.entities:
            if nexus.alive:
                self._update_individual_stress(nexus, world, global_stress)
                self._apply_stress_effects(nexus, world)
                self._check_anomalous_behavior(nexus, world)
    
    def _update_population_stats(self, world):
        """Actualiza las estadísticas poblacionales.
        
        Args:
            world (World): Referencia al mundo de la simulación.
        """
        alive_entities = [e for e in world.entities if e.alive]
        self.population_stats["total_population"] = len(alive_entities)
        
        # Resetear contadores
        self.population_stats["stress_levels"].clear()
        self.population_stats["anomalous_count"].clear()
        
        # Contar niveles de estrés
        for nexus in alive_entities:
            stress_level = self._get_stress_level(nexus)
            self.population_stats["stress_levels"][stress_level] += 1
            
            # Contar comportamientos anómalos
            nexus_id = id(nexus)
            if nexus_id in self.anomalous_individuals:
                behavior_type = self.anomalous_individuals[nexus_id]
                self.population_stats["anomalous_count"][behavior_type] += 1
        
        # Calcular colapso social (porcentaje con estrés alto/crítico)
        high_stress = (self.population_stats["stress_levels"]["high"] + 
                      self.population_stats["stress_levels"]["critical"])
        total = max(1, self.population_stats["total_population"])
        self.population_stats["social_breakdown"] = high_stress / total
    
    def _calculate_global_stress(self, world):
        """Calcula factores de estrés globales.
        
        Args:
            world (World): Referencia al mundo de la simulación.
            
        Returns:
            dict: Factores de estrés globales.
        """
        global_stress = {
            "population_density": 0.0,
            "resource_availability": 1.0,
            "territory_pressure": 0.0
        }
        
        # Densidad poblacional
        total_area = world.config.WORLD_SIZE[0] * world.config.WORLD_SIZE[1]
        population = self.population_stats["total_population"]
        density = population / (total_area / 1000000)  # Por km²
        global_stress["population_density"] = min(1.0, density / 50)  # Normalizar
        
        # Disponibilidad de recursos
        total_resources = len(getattr(world, "resources", []))
        resource_ratio = total_resources / max(1, population)
        global_stress["resource_availability"] = max(0.0, min(1.0, resource_ratio / 5))
        
        # Presión territorial (basada en conflictos)
        territory_conflicts = 0
        for nexus in world.entities:
            if nexus.alive and hasattr(nexus, "territory_conflicts"):
                territory_conflicts += nexus.territory_conflicts
        
        if population > 0:
            global_stress["territory_pressure"] = min(1.0, territory_conflicts / population)
        
        return global_stress
    
    def _update_individual_stress(self, nexus, world, global_stress):
        """Actualiza el nivel de estrés individual.
        
        Args:
            nexus (Nexus): Nexus a actualizar.
            world (World): Referencia al mundo.
            global_stress (dict): Factores de estrés globales.
        """
        # Inicializar estrés si no existe
        if not hasattr(nexus, "stress_level"):
            nexus.stress_level = 0.0
        
        stress_increase = 0.0
        
        # Estrés por hacinamiento
        nearby_count = len(world.get_nearby_nexus(
            nexus.position, 
            self.stress_factors["overcrowding"]["radius"], 
            exclude=nexus
        ))
        
        if nearby_count > self.stress_factors["overcrowding"]["threshold"]:
            excess = nearby_count - self.stress_factors["overcrowding"]["threshold"]
            stress_increase += excess * self.stress_factors["overcrowding"]["intensity"]
        
        # Estrés por competencia de recursos
        nearby_resources = world.get_nearby_resources(nexus.position, 100)
        if nearby_resources:
            for resource in nearby_resources:
                competitors = len(world.get_nearby_nexus(
                    resource.position, 
                    self.stress_factors["resource_competition"]["radius"],
                    exclude=nexus
                ))
                
                if competitors > self.stress_factors["resource_competition"]["threshold"]:
                    excess = competitors - self.stress_factors["resource_competition"]["threshold"]
                    stress_increase += excess * self.stress_factors["resource_competition"]["intensity"]
        
        # Estrés por aislamiento social
        last_social_time = self._get_last_social_interaction(nexus)
        time_since_social = nexus.age - last_social_time
        
        if time_since_social > self.stress_factors["social_isolation"]["threshold"]:
            isolation_factor = (time_since_social - self.stress_factors["social_isolation"]["threshold"]) / 1000
            stress_increase += isolation_factor * self.stress_factors["social_isolation"]["intensity"]
        
        # Estrés por salud baja
        if nexus.health < self.stress_factors["health_decline"]["threshold"]:
            health_loss = self.stress_factors["health_decline"]["threshold"] - nexus.health
            stress_increase += health_loss * self.stress_factors["health_decline"]["intensity"] / 100
        
        # Estrés por factores globales
        stress_increase += global_stress["population_density"] * 0.1
        stress_increase += (1.0 - global_stress["resource_availability"]) * 0.15
        stress_increase += global_stress["territory_pressure"] * 0.1
        
        # Aplicar incremento de estrés
        nexus.stress_level = min(1.0, nexus.stress_level + stress_increase)
        
        # Reducción natural del estrés (muy lenta)
        if stress_increase == 0:
            nexus.stress_level = max(0.0, nexus.stress_level - 0.001)
    
    def _get_last_social_interaction(self, nexus):
        """Obtiene el tiempo de la última interacción social.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            
        Returns:
            int: Tiempo de la última interacción social.
        """
        last_social = 0
        for memory in nexus.memory:
            if any(word in memory["text"].lower() for word in 
                   ["socializó", "comunicó", "enseñó", "aprendió", "reprodujo"]):
                last_social = max(last_social, memory["time"])
        
        return last_social
    
    def _get_stress_level(self, nexus):
        """Obtiene el nivel de estrés categórico.
        
        Args:
            nexus (Nexus): Nexus a evaluar.
            
        Returns:
            str: Nivel de estrés ('low', 'moderate', 'high', 'critical').
        """
        if not hasattr(nexus, "stress_level"):
            return "low"
        
        stress = nexus.stress_level
        
        for level, (min_val, max_val) in self.stress_effects.items():
            if min_val <= stress < max_val:
                return level
        
        return "critical"
    
    def _apply_stress_effects(self, nexus, world):
        """Aplica los efectos del estrés en el comportamiento.
        
        Args:
            nexus (Nexus): Nexus afectado.
            world (World): Referencia al mundo.
        """
        stress_level = self._get_stress_level(nexus)
        
        if stress_level == "low":
            return  # Sin efectos
        
        # Modificar atributos según el estrés
        if stress_level in ["moderate", "high", "critical"]:
            # Reducir eficiencia en actividades
            if hasattr(nexus, "efficiency_modifier"):
                nexus.efficiency_modifier *= (1.0 - nexus.stress_level * 0.3)
            else:
                nexus.efficiency_modifier = 1.0 - nexus.stress_level * 0.3
            
            # Afectar emociones
            nexus.emotions["fear"] = min(1.0, nexus.emotions.get("fear", 0) + nexus.stress_level * 0.2)
            nexus.emotions["anger"] = min(1.0, nexus.emotions.get("anger", 0) + nexus.stress_level * 0.15)
            nexus.emotions["happiness"] = max(0.0, nexus.emotions.get("happiness", 0.5) - nexus.stress_level * 0.3)
        
        # Comportamientos específicos por nivel de estrés
        if stress_level in self.stress_behaviors:
            possible_behaviors = self.stress_behaviors[stress_level]
            
            # Probabilidad de cambiar a comportamiento de estrés
            if random.random() < nexus.stress_level * 0.3:
                stress_behavior = random.choice(possible_behaviors)
                
                # Cambiar estado si no está en una actividad crítica
                if nexus.state not in ["fleeing", "seeking_food", "resting"]:
                    nexus.state = stress_behavior
                    
                    # Añadir memoria del comportamiento de estrés
                    nexus.add_memory(f"Experimentó comportamiento de estrés: {stress_behavior}")

    def _check_anomalous_behavior(self, nexus, world):
        """Verifica y aplica comportamientos anómalos (Universe 25).

        Args:
            nexus (Nexus): Nexus a evaluar.
            world (World): Referencia al mundo.
        """
        nexus_id = id(nexus)
        stress_level = self._get_stress_level(nexus)

        # Solo desarrollar comportamientos anómalos con estrés alto/crítico
        if stress_level not in ["high", "critical"]:
            return

        # Si ya tiene un comportamiento anómalo, mantenerlo
        if nexus_id in self.anomalous_individuals:
            self._apply_anomalous_behavior(nexus, self.anomalous_individuals[nexus_id])
            return

        # Probabilidad de desarrollar comportamiento anómalo
        anomaly_chance = nexus.stress_level * 0.1  # 10% máximo con estrés crítico

        if random.random() < anomaly_chance:
            # Seleccionar tipo de comportamiento anómalo basado en personalidad
            behavior_type = self._select_anomalous_behavior(nexus)
            self.anomalous_individuals[nexus_id] = behavior_type

            # Añadir memoria del cambio
            nexus.add_memory(f"Desarrolló comportamiento anómalo: {behavior_type}")

            # Aplicar inmediatamente
            self._apply_anomalous_behavior(nexus, behavior_type)

    def _select_anomalous_behavior(self, nexus):
        """Selecciona un comportamiento anómalo basado en la personalidad.

        Args:
            nexus (Nexus): Nexus a evaluar.

        Returns:
            str: Tipo de comportamiento anómalo.
        """
        # Probabilidades basadas en personalidad
        if nexus.personality.get("sociability", 0.5) < 0.3:
            # Individuos poco sociales tienden a retirarse
            if random.random() < 0.6:
                return "beautiful_ones"
            else:
                return "passive_withdrawn"

        elif nexus.personality.get("aggression", 0.5) > 0.7:
            # Individuos agresivos se vuelven hiperdomínantes
            return "aggressive_dominants"

        elif nexus.personality.get("curiosity", 0.5) > 0.7:
            # Individuos curiosos se vuelven hiperactividad sin propósito
            return "hyperactive_wanderers"

        else:
            # Selección aleatoria para otros casos
            return random.choice(list(self.anomalous_behaviors.keys()))

    def _apply_anomalous_behavior(self, nexus, behavior_type):
        """Aplica un comportamiento anómalo específico.

        Args:
            nexus (Nexus): Nexus afectado.
            behavior_type (str): Tipo de comportamiento anómalo.
        """
        if behavior_type not in self.anomalous_behaviors:
            return

        behavior_data = self.anomalous_behaviors[behavior_type]
        traits = behavior_data["traits"]

        # Modificar comportamiento según el tipo
        if behavior_type == "beautiful_ones":
            # Se retiran socialmente, se acicalan excesivamente
            if nexus.state not in ["fleeing", "seeking_food"]:
                if random.random() < 0.7:
                    nexus.state = "grooming"
                elif random.random() < 0.3:
                    nexus.state = "withdrawing"

            # Reducir drasticamente la reproducción
            if hasattr(nexus, "reproduction_desire"):
                nexus.reproduction_desire *= 0.1

            # Evitar interacciones sociales
            nexus.personality["sociability"] = min(nexus.personality["sociability"], 0.1)

        elif behavior_type == "aggressive_dominants":
            # Hiperdominio agresivo
            if nexus.state not in ["fleeing", "seeking_food"]:
                if random.random() < 0.5:
                    nexus.state = "aggressive"
                elif random.random() < 0.3:
                    nexus.state = "patrolling"
                elif random.random() < 0.2:
                    nexus.state = "establishing_territory"

            # Aumentar agresividad
            nexus.personality["aggression"] = min(1.0, nexus.personality["aggression"] + 0.3)

            # Aumentar territorialidad
            if not hasattr(nexus, "territory_size"):
                nexus.territory_size = 200
            else:
                nexus.territory_size = min(500, nexus.territory_size * 1.5)

        elif behavior_type == "passive_withdrawn":
            # Pasivos y retraídos
            if nexus.state not in ["fleeing", "seeking_food"]:
                if random.random() < 0.8:
                    nexus.state = "withdrawing"
                else:
                    nexus.state = "idle"

            # Reducir actividad general
            nexus.personality["curiosity"] = min(nexus.personality["curiosity"], 0.2)
            nexus.personality["sociability"] = min(nexus.personality["sociability"], 0.1)

            # Reducir velocidad de movimiento
            if hasattr(nexus, "speed_modifier"):
                nexus.speed_modifier *= 0.5
            else:
                nexus.speed_modifier = 0.5

        elif behavior_type == "hyperactive_wanderers":
            # Hiperactividad sin propósito
            if nexus.state not in ["fleeing", "seeking_food"]:
                if random.random() < 0.6:
                    nexus.state = "exploring"
                elif random.random() < 0.3:
                    nexus.state = "migrating"
                else:
                    nexus.state = "competing"

            # Aumentar velocidad pero reducir eficiencia
            if hasattr(nexus, "speed_modifier"):
                nexus.speed_modifier *= 1.5
            else:
                nexus.speed_modifier = 1.5

            if hasattr(nexus, "efficiency_modifier"):
                nexus.efficiency_modifier *= 0.3
            else:
                nexus.efficiency_modifier = 0.3

    def get_population_report(self):
        """Genera un reporte del estado poblacional.

        Returns:
            dict: Reporte detallado del estado poblacional.
        """
        total = max(1, self.population_stats["total_population"])

        report = {
            "total_population": total,
            "social_breakdown_percentage": self.population_stats["social_breakdown"] * 100,
            "stress_distribution": {
                level: count / total * 100
                for level, count in self.population_stats["stress_levels"].items()
            },
            "anomalous_behaviors": {
                behavior: count / total * 100
                for behavior, count in self.population_stats["anomalous_count"].items()
            },
            "critical_indicators": {
                "high_stress_population": (
                    self.population_stats["stress_levels"]["high"] +
                    self.population_stats["stress_levels"]["critical"]
                ) / total * 100,
                "social_withdrawal": (
                    self.population_stats["anomalous_count"]["beautiful_ones"] +
                    self.population_stats["anomalous_count"]["passive_withdrawn"]
                ) / total * 100,
                "aggressive_behavior": (
                    self.population_stats["anomalous_count"]["aggressive_dominants"]
                ) / total * 100
            }
        }

        return report

    def is_population_collapsing(self):
        """Determina si la población está en colapso social.

        Returns:
            bool: True si la población está colapsando.
        """
        # Criterios de colapso social (inspirados en Universe 25)
        breakdown_threshold = 0.6  # 60% con estrés alto/crítico
        anomaly_threshold = 0.4    # 40% con comportamientos anómalos

        total_anomalous = sum(self.population_stats["anomalous_count"].values())
        total_population = max(1, self.population_stats["total_population"])
        anomaly_rate = total_anomalous / total_population

        return (self.population_stats["social_breakdown"] > breakdown_threshold or
                anomaly_rate > anomaly_threshold)
