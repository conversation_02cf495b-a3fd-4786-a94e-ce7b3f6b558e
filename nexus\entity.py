"""Módulo de entidad Nexus.

Define la clase Nexus, que representa a los individuos en la simulación.
"""

import pygame
import random
import math
from pygame.locals import *

class Nexus:
    """Clase que representa a un individuo Nexus en la simulación."""

    def __init__(self, name, position, attributes=None, parent1=None, parent2=None):
        """Inicializa un nuevo Nexus.

        Args:
            name (str): Nombre del Nexus.
            position (tuple): Posición inicial (x, y).
            attributes (dict, optional): Atributos iniciales.
            parent1 (Nexus, optional): Primer progenitor.
            parent2 (Nexus, optional): Segundo progenitor.
        """
        self.name = name
        self.position = list(position)
        self.velocity = [0, 0]
        self.direction = 0  # Dirección en radianes (0 = derecha, PI/2 = abajo, PI = izquierda, 3PI/2 = arriba)
        self.age = 0
        self.energy = 100  # Energía inicial
        self.health = 100  # Salud inicial
        self.alive = True
        self.target = None  # Objetivo actual (comida, pareja, etc.)
        self.state = "moving"  # Estado actual (idle, hunting, sleeping, etc.)
        self.sex = random.choice(["male", "female"])  # Sexo biológico
        self.selected = False  # Indica si el Nexus está seleccionado por el usuario
        self.reproduction_mode = random.choice(["sexual", "asexual"])  # Modo de reproducción inicial
        self.sexual_maturity_age = 16 * 30  # Edad de madurez sexual en días simulados (aprox 16 años)
        self.max_age = 120 * 30  # Edad máxima en días simulados (120 años)
        self.gestation_time = 9 * 30  # 9 meses simulados (270 días)
        self.gestation_counter = 0
        self.is_pregnant = False
        self.pregnancy_partner = None
        self.offspring_to_generate = 0

        # Atributos físicos y mentales
        self.attributes = attributes or {
            "strength": random.uniform(0.3, 0.7),      # Fuerza física
            "speed": random.uniform(0.3, 0.7),        # Velocidad de movimiento
            "intelligence": random.uniform(0.3, 0.7),  # Capacidad de aprendizaje
            "curiosity": random.uniform(0.3, 0.7),    # Tendencia a explorar
            "selfishness": random.uniform(0.3, 0.7),  # Tendencia a no compartir
        }

        # Si tiene padres, hereda atributos
        if parent1 and parent2:
            self.inherit_attributes(parent1, parent2)

        # Personalidad y emociones
        self.personality = {
            "aggression": random.uniform(0.1, 0.9),
            "sociability": random.uniform(0.1, 0.9),
            "bravery": random.uniform(0.1, 0.9),
        }

        self.emotions = {
            "happiness": 0.5,
            "fear": 0.0,
            "anger": 0.0,
        }

        # Conocimientos y memoria
        self.knowledge = set()  # Conjunto de habilidades conocidas
        self.memory = []  # Lista de recuerdos (eventos importantes)

        # Creencias y tradiciones culturales
        self.beliefs = []  # Creencias adoptadas
        self.participated_rituals = []  # Rituales en los que ha participado

        # Relaciones sociales
        self.relationships = {}  # Diccionario de relaciones con otros Nexus
        self.tribe = None  # Tribu a la que pertenece
        self.social_status = 0.5  # Estatus social inicial

        # Representación visual
        self.color = (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255))
        self.size = 20

    def inherit_attributes(self, parent1, parent2):
        """Hereda atributos de los padres con posibles mutaciones.

        Args:
            parent1 (Nexus): Primer progenitor.
            parent2 (Nexus): Segundo progenitor.
        """
        for attr in self.attributes:
            # Herencia mezclada de ambos padres
            p1_value = parent1.attributes.get(attr, 0.5)
            p2_value = parent2.attributes.get(attr, 0.5)

            # Valor base heredado (promedio de los padres)
            base_value = (p1_value + p2_value) / 2

            # Aplicar mutación aleatoria
            mutation = random.uniform(-0.1, 0.1)
            self.attributes[attr] = max(0.0, min(1.0, base_value + mutation))

    def update(self, world):
        """Actualiza el estado del Nexus en cada tick.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        if not self.alive:
            return

        # Guardar referencia al mundo
        self.world = world

        # Envejecer
        self.age += 1

        # Consumir energía (reducido)
        self.energy -= 0.02  # Consumo base de energía reducido

        # Verificar si muere por falta de energía
        if self.energy <= 0 or self.health <= 0:
            self.die()
            return

        # Actualizar estado emocional
        self.update_emotions(world)

        # Actualizar sistemas avanzados si están disponibles
        if hasattr(world, "resource_management_system"):
            world.resource_management_system.update(self, world)

        # Tomar decisiones según el estado actual (mejorado con IA autónoma)
        if hasattr(world, "autonomous_ai_system"):
            world.autonomous_ai_system.update(self, world)
        else:
            self.make_decision(world)

        # Lógica de reproducción
        self.handle_reproduction(world)

        # Aplicar física realista si el sistema está disponible
        if hasattr(world, "physics_system"):
            # Usar el sistema de física avanzado
            world.physics_system.apply_physics(self, world)
        else:
            # Actualización de posición básica (compatibilidad)
            self.position[0] += self.velocity[0]
            self.position[1] += self.velocity[1]

            # Actualizar dirección basada en la velocidad
            if abs(self.velocity[0]) > 0.01 or abs(self.velocity[1]) > 0.01:
                self.direction = math.atan2(self.velocity[1], self.velocity[0])

            # Aplicar fricción básica
            self.velocity[0] *= 0.9
            self.velocity[1] *= 0.9

        # Mantener dentro de los límites del mundo circular
        if hasattr(world, 'is_in_world') and not world.is_in_world(self.position):
            # Si está fuera del mundo, aplicar fuerza hacia el centro
            if hasattr(world, 'get_border_force'):
                force = world.get_border_force(self.position)
                self.velocity[0] += force[0]
                self.velocity[1] += force[1]

                # Actualizar posición con la nueva velocidad
                self.position[0] += self.velocity[0]
                self.position[1] += self.velocity[1]

                # Añadir memoria sobre haber llegado al borde
                if random.random() < 0.1:  # Solo ocasionalmente para no saturar la memoria
                    self.add_memory("Llegó al borde del mundo y tuvo que regresar")

    def update_emotions(self, world):
        """Actualiza el estado emocional según el entorno.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Si existe el sistema de emociones avanzado, usarlo
        if hasattr(world, "emotion_system"):
            # El sistema de emociones se encarga de todo
            return

        # Sistema básico de emociones (para compatibilidad)
        # Ejemplo: aumentar miedo si hay depredadores cerca
        predators_nearby = world.get_nearby_predators(self.position, 200)
        if predators_nearby:
            self.emotions["fear"] = min(1.0, self.emotions["fear"] + 0.1)
        else:
            self.emotions["fear"] = max(0.0, self.emotions["fear"] - 0.01)

        # Ejemplo: aumentar felicidad si tiene energía alta
        if self.energy > 80:
            self.emotions["happiness"] = min(1.0, self.emotions["happiness"] + 0.01)
        elif self.energy < 30:
            self.emotions["happiness"] = max(0.0, self.emotions["happiness"] - 0.01)

        # Actualizar emoción de ira/odio
        if "hatred" not in self.emotions:
            self.emotions["hatred"] = 0.0

        # Reducir odio gradualmente con el tiempo
        self.emotions["hatred"] = max(0.0, self.emotions["hatred"] - 0.001)

        # Buscar Nexus cercanos que puedan generar odio
        nearby_nexus = world.get_nearby_nexus(self.position, 150, exclude=self)
        for other in nearby_nexus:
            # Verificar si hay una relación negativa
            if other.name in self.relationships and self.relationships[other.name] < -0.5:
                # Aumentar odio basado en la negatividad de la relación
                hatred_increase = abs(self.relationships[other.name]) * 0.01
                self.emotions["hatred"] = min(1.0, self.emotions["hatred"] + hatred_increase)

        # Aplicar efectos de las creencias en las emociones
        if hasattr(self, "beliefs") and self.beliefs:
            for belief in self.beliefs:
                if hasattr(belief, "apply_effects"):
                    belief.apply_effects(self)

        # Aplicar efectos de rituales recientes
        if hasattr(self, "participated_rituals") and self.participated_rituals:
            # Filtrar rituales recientes (últimos 100 ticks)
            recent_rituals = [r for r in self.participated_rituals if world.time - r["time"] < 100]
            self.participated_rituals = recent_rituals

            # Aplicar efecto emocional residual de rituales recientes
            if recent_rituals:
                self.emotions["happiness"] = min(1.0, self.emotions["happiness"] + 0.005 * len(recent_rituals))

    def make_decision(self, world):
        """Toma una decisión sobre qué hacer a continuación.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Prioridad 0: Supervivencia crítica (salud baja)
        if self.health < 30:
            # Buscar refugio si existe
            if hasattr(world, "shelter_system"):
                nearest_shelter = world.shelter_system.get_nearest_shelter(self.position, self.tribe)
                if nearest_shelter:
                    self.state = "seeking_shelter"
                    self.seek_shelter(nearest_shelter)
                    return

            # Si no hay refugio, huir de depredadores
            self.state = "seeking_shelter"
            self.flee_from_predators(world)
            return

        # Prioridad 1: Ayudar a miembros de la tribu bajo ataque
        if self.tribe:
            tribe_members_under_attack = []
            for member in self.tribe.members:
                if member != self and member.alive and member.state == "under_attack":
                    # Calcular distancia
                    distance = math.sqrt((self.position[0] - member.position[0])**2 +
                                       (self.position[1] - member.position[1])**2)
                    if distance < 300:  # Solo ayudar si está relativamente cerca
                        tribe_members_under_attack.append((member, distance))

            # Ordenar por distancia (ayudar primero al más cercano)
            if tribe_members_under_attack:
                tribe_members_under_attack.sort(key=lambda x: x[1])
                member_to_help, _ = tribe_members_under_attack[0]

                # Ir a ayudar
                self.state = "helping_tribe"
                self.help_tribe_member(member_to_help, world)
                return

        # Prioridad 2: Buscar refugio en condiciones adversas
        if ((hasattr(world, "weather") and world.weather != "clear" and world.weather_intensity > 0.7) or
            (hasattr(world, "time_of_day") and world.time_of_day == "night")):

            if hasattr(world, "shelter_system"):
                nearest_shelter = world.shelter_system.get_nearest_shelter(self.position, self.tribe)
                if nearest_shelter:
                    distance = math.sqrt((self.position[0] - nearest_shelter.position[0])**2 +
                                       (self.position[1] - nearest_shelter.position[1])**2)

                    if distance < 500:  # Si el refugio está a una distancia razonable
                        self.state = "seeking_shelter"
                        self.seek_shelter(nearest_shelter)
                        return

        # Prioridad 3: Huir de depredadores
        predators_nearby = world.get_nearby_predators(self.position, 150)
        if predators_nearby:
            # Decidir si huir o atacar basado en número de aliados cercanos y personalidad
            nearby_allies = world.get_nearby_nexus(self.position, 100, exclude=self)
            nearby_allies = [n for n in nearby_allies if n.alive]

            # Si hay suficientes aliados y es valiente, atacar
            if (len(nearby_allies) >= len(predators_nearby) * 2 and
                self.personality["bravery"] > 0.7 and
                self.attributes["strength"] > 0.5):
                self.state = "attacking"
                self.attack_predator(predators_nearby[0], world)

                # Llamar a los aliados para que ayuden
                for ally in nearby_allies:
                    if random.random() < ally.personality["bravery"] * 0.5:
                        ally.state = "helping_tribe"
                        ally.help_tribe_member(self, world)
            else:
                # Huir
                self.state = "fleeing"
                self.flee_from_predators(world)
            return

        # Prioridad 4: Buscar comida si tiene hambre
        if self.energy < 50:
            self.state = "seeking_food"
            self.seek_food(world)
            return

        # Prioridad 5: Construir o mejorar refugio
        if (hasattr(self, "knowledge") and
            "Construir refugios" in self.knowledge and
            random.random() < 0.05 * self.attributes["intelligence"]):

            if hasattr(world, "shelter_system"):
                # Verificar si la tribu ya tiene refugio
                tribe_has_shelter = False
                if self.tribe:
                    for shelter in world.shelter_system.shelters:
                        if shelter.tribe == self.tribe:
                            tribe_has_shelter = True

                            # Posibilidad de mejorar el refugio existente
                            if random.random() < 0.2 and shelter.construction_level < 10:
                                distance = math.sqrt((self.position[0] - shelter.position[0])**2 +
                                                   (self.position[1] - shelter.position[1])**2)

                                if distance < 100:  # Si está cerca del refugio
                                    self.state = "improving_shelter"
                                    shelter.improve(self)
                                    self.add_memory(f"Mejoró el refugio de la tribu")
                                    return
                                elif distance < 300:  # Si está a distancia media
                                    self.state = "seeking_shelter"
                                    self.seek_shelter(shelter)
                                    return
                            break

                # Si no tiene refugio, posibilidad de iniciar construcción
                if not tribe_has_shelter and self.tribe and random.random() < 0.1:
                    self.state = "building_shelter"
                    self.add_memory("Decidió que la tribu necesita construir un refugio")
                    world.shelter_system.create_shelter_for_tribe(self.tribe)
                    return

        # Prioridad 6: Descansar si está cansado
        if self.energy < 70:
            # Intentar descansar en un refugio si hay uno cerca
            if hasattr(world, "shelter_system"):
                nearest_shelter = world.shelter_system.get_nearest_shelter(self.position, self.tribe)
                if nearest_shelter:
                    distance = math.sqrt((self.position[0] - nearest_shelter.position[0])**2 +
                                       (self.position[1] - nearest_shelter.position[1])**2)

                    if distance < 200:  # Si el refugio está cerca
                        self.state = "seeking_shelter"
                        self.seek_shelter(nearest_shelter)
                        return

            # Si no hay refugio cerca, descansar donde está
            self.state = "resting"
            # Reducir velocidad para descansar
            self.velocity = [self.velocity[0] * 0.5, self.velocity[1] * 0.5]
            # Recuperar energía más rápido cuando descansa
            self.energy = min(100, self.energy + 0.1)
            return

        # Prioridad 7: Socializar con otros Nexus
        nearby_nexus = world.get_nearby_nexus(self.position, 100, exclude=self)
        if nearby_nexus and random.random() < self.personality["sociability"] * 0.3:
            self.state = "socializing"
            self.socialize_with(random.choice(nearby_nexus))
            return

        # Prioridad 8: Explorar (comportamiento por defecto)
        self.state = "exploring"
        self.explore(world)

    def help_tribe_member(self, member, world):
        """Ayuda a un miembro de la tribu que está siendo atacado.

        Args:
            member (Nexus): Miembro de la tribu a ayudar.
            world (World): Referencia al mundo de la simulación.
        """
        # Moverse hacia el miembro
        direction_x = member.position[0] - self.position[0]
        direction_y = member.position[1] - self.position[1]

        # Normalizar el vector
        length = math.sqrt(direction_x**2 + direction_y**2)
        if length > 0:
            direction_x /= length
            direction_y /= length

        # Establecer velocidad (rápido para ayudar)
        speed_multiplier = 1.3  # Más rápido para llegar a tiempo
        self.velocity[0] = direction_x * self.attributes["speed"] * speed_multiplier
        self.velocity[1] = direction_y * self.attributes["speed"] * speed_multiplier

        # Si está cerca y hay un depredador, atacarlo
        distance_to_member = math.sqrt((self.position[0] - member.position[0])**2 +
                                     (self.position[1] - member.position[1])**2)

        if distance_to_member < 50:
            # Buscar depredadores cercanos al miembro
            predators = world.get_nearby_predators(member.position, 50)
            if predators:
                self.attack_predator(predators[0], world)

                # Añadir recuerdo
                self.add_memory(f"Ayudó a {member.name} contra un depredador")
                member.add_memory(f"Recibió ayuda de {self.name} contra un depredador")

                # Mejorar relación
                if member.name not in self.relationships:
                    self.relationships[member.name] = {"value": 0.5, "type": "tribe_member"}
                if self.name not in member.relationships:
                    member.relationships[self.name] = {"value": 0.5, "type": "tribe_member"}

                self.relationships[member.name]["value"] = min(1.0, self.relationships[member.name]["value"] + 0.2)
                member.relationships[self.name]["value"] = min(1.0, member.relationships[self.name]["value"] + 0.3)

    def attack_predator(self, predator, world):
        """Ataca a un depredador.

        Args:
            predator: Depredador a atacar.
            world (World): Referencia al mundo de la simulación.
        """
        # Moverse hacia el depredador
        direction_x = predator.position[0] - self.position[0]
        direction_y = predator.position[1] - self.position[1]

        # Normalizar el vector
        length = math.sqrt(direction_x**2 + direction_y**2)
        if length > 0:
            direction_x /= length
            direction_y /= length

        # Establecer velocidad
        self.velocity[0] = direction_x * self.attributes["speed"]
        self.velocity[1] = direction_y * self.attributes["speed"]

        # Si está lo suficientemente cerca, atacar
        distance = math.sqrt((self.position[0] - predator.position[0])**2 +
                           (self.position[1] - predator.position[1])**2)

        if distance < self.size + predator.size + 5:
            # Calcular daño basado en fuerza
            damage = int(self.attributes["strength"] * 10)

            # Aplicar daño al depredador
            if hasattr(predator, "health"):
                predator.health -= damage

                # Añadir recuerdo
                self.add_memory(f"Atacó a un depredador causando {damage} de daño")

                # Verificar si el depredador ha muerto
                if predator.health <= 0:
                    # Eliminar el depredador
                    if hasattr(predator, "die"):
                        predator.die()
                    else:
                        world.predators.remove(predator)

                    # Añadir recuerdo y aumentar confianza
                    self.add_memory("Mató a un depredador")
                    self.emotions["fear"] = max(0.0, self.emotions["fear"] - 0.3)
                    self.personality["bravery"] = min(1.0, self.personality["bravery"] + 0.1)

                    # Notificar a la tribu
                    if self.tribe:
                        for member in self.tribe.members:
                            if member != self and member.alive:
                                member.add_memory(f"{self.name} mató a un depredador")
                                member.emotions["fear"] = max(0.0, member.emotions["fear"] - 0.1)
                else:
                    # Posibilidad de que el depredador huya si está muy herido
                    if predator.health < 30 and random.random() < 0.7:
                        # Hacer que el depredador huya
                        if hasattr(predator, "target"):
                            predator.target = None

                        # Dirección opuesta al Nexus
                        flee_x = predator.position[0] - self.position[0]
                        flee_y = predator.position[1] - self.position[1]
                        flee_length = math.sqrt(flee_x**2 + flee_y**2)

                        if flee_length > 0:
                            flee_x /= flee_length
                            flee_y /= flee_length

                            predator.velocity[0] = flee_x * predator.speed * 1.5
                            predator.velocity[1] = flee_y * predator.speed * 1.5

    def flee_from_predators(self, world):
        """Huye de los depredadores cercanos.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        predators = world.get_nearby_predators(self.position, 200)
        if not predators:
            return

        # Calcular dirección opuesta a los depredadores
        avg_predator_x = sum(p.position[0] for p in predators) / len(predators)
        avg_predator_y = sum(p.position[1] for p in predators) / len(predators)

        # Vector desde el depredador promedio hacia el Nexus
        direction_x = self.position[0] - avg_predator_x
        direction_y = self.position[1] - avg_predator_y

        # Normalizar el vector
        length = math.sqrt(direction_x**2 + direction_y**2)
        if length > 0:
            direction_x /= length
            direction_y /= length

        # Establecer velocidad (más rápido cuando huye)
        speed_multiplier = 1.5  # Huir más rápido
        self.velocity[0] = direction_x * self.attributes["speed"] * speed_multiplier
        self.velocity[1] = direction_y * self.attributes["speed"] * speed_multiplier

    def seek_food(self, world):
        """Busca comida cercana, incluyendo recursos, frutas, plantas y animales.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Buscar todas las fuentes de alimento cercanas
        food_sources = []

        # 1. Recursos de comida
        for resource in world.resources:
            if resource.type == "food" and not resource.consumed:
                distance = math.sqrt((self.position[0] - resource.position[0])**2 +
                                    (self.position[1] - resource.position[1])**2)
                food_sources.append(("resource", resource, distance, resource.nutritional_value))

        # 2. Árboles frutales
        for tree in world.trees:
            if tree.type == "fruit" and tree.fruits:
                distance = math.sqrt((self.position[0] - tree.position[0])**2 +
                                   (self.position[1] - tree.position[1])**2)
                # Valor nutricional promedio de las frutas
                avg_value = tree.fruit_nutritional_value * len(tree.fruits)
                food_sources.append(("tree", tree, distance, avg_value))

        # 3. Plantas con bayas
        for plant in world.plants:
            if hasattr(plant, "has_berries") and plant.has_berries and plant.is_harvestable:
                distance = math.sqrt((self.position[0] - plant.position[0])**2 +
                                   (self.position[1] - plant.position[1])**2)
                food_sources.append(("plant", plant, distance, plant.nutritional_value))

        # 4. Animales (si tiene conocimiento de caza)
        if "Cazar en grupo" in self.knowledge or "Fabricar lanza" in self.knowledge:
            for animal in world.animals:
                if animal.is_alive:
                    distance = math.sqrt((self.position[0] - animal.position[0])**2 +
                                       (self.position[1] - animal.position[1])**2)
                    # Ajustar valor según dificultad de caza
                    hunting_difficulty = animal.size / 10  # Mayor tamaño, más difícil
                    adjusted_value = animal.nutritional_value / hunting_difficulty
                    food_sources.append(("animal", animal, distance, adjusted_value))

        # 5. Peces en cuerpos de agua (si tiene conocimiento de pesca)
        if "Pescar" in self.knowledge:
            for water in world.water_bodies:
                if water.fish_population > 0:
                    distance = math.sqrt((self.position[0] - water.position[0])**2 +
                                       (self.position[1] - water.position[1])**2)
                    # Valor ajustado por cantidad de peces
                    fish_value = 10 * water.fish_population * water.water_quality
                    food_sources.append(("water", water, distance, fish_value))

        # Ordenar fuentes de alimento por una combinación de distancia y valor nutricional
        # Fórmula: valor / (distancia + 1) para evitar división por cero
        food_sources.sort(key=lambda x: x[3] / (x[2] + 1), reverse=True)

        # Si encuentra fuentes de alimento, moverse hacia la mejor
        if food_sources:
            best_source = food_sources[0]
            source_type, source, distance, _ = best_source

            # Si está muy cerca, consumir el alimento
            if distance < self.size + 20:  # Radio de interacción
                self.consume_food_source(source_type, source, world)
                return

            # Moverse hacia la fuente de alimento
            target_pos = source.position
            direction_x = target_pos[0] - self.position[0]
            direction_y = target_pos[1] - self.position[1]

            # Normalizar el vector
            length = math.sqrt(direction_x**2 + direction_y**2)
            if length > 0:
                direction_x /= length
                direction_y /= length

            # Establecer velocidad
            self.velocity[0] = direction_x * self.attributes["speed"]
            self.velocity[1] = direction_y * self.attributes["speed"]
        else:
            # Si no hay comida visible, explorar
            self.explore(world)

    def consume_food_source(self, source_type, source, world):
        """Consume una fuente de alimento.

        Args:
            source_type (str): Tipo de fuente ("resource", "tree", "plant", "animal", "water").
            source: La fuente de alimento.
            world (World): Referencia al mundo.
        """
        energy_gained = 0
        memory_text = ""

        if source_type == "resource":
            # Consumir recurso
            energy_gained = source.nutritional_value
            source.consumed = True
            memory_text = f"Consumió un recurso de {source.type} y recuperó energía"

        elif source_type == "tree" and source.fruits:
            # Recolectar fruta
            fruit = source.fruits[0]  # Tomar la primera fruta
            energy_gained = fruit.nutritional_value
            fruit.consumed = True
            source.fruits.remove(fruit)
            memory_text = "Recolectó fruta de un árbol"

        elif source_type == "plant" and hasattr(source, "has_berries") and source.has_berries:
            # Recolectar bayas
            harvest = source.harvest()
            energy_gained = harvest["food"]
            source.has_berries = False  # Ya no tiene bayas
            memory_text = "Recolectó bayas de un arbusto"

        elif source_type == "animal" and source.is_alive:
            # Cazar animal
            # La caza es más exitosa con mayor fuerza y conocimiento
            hunt_success = self.attributes["strength"] * 0.5

            if "Fabricar lanza" in self.knowledge:
                hunt_success += 0.3

            if "Cazar en grupo" in self.knowledge:
                # Buscar aliados cercanos
                allies = world.get_nearby_nexus(self.position, 100, exclude=self)
                hunt_success += len(allies) * 0.1

            # Intentar caza
            if random.random() < hunt_success:
                energy_gained = source.hunt()
                memory_text = f"Cazó un {source.type} con éxito"

                # Aprender de la experiencia
                self.attributes["strength"] = min(1.0, self.attributes["strength"] + 0.01)

                # Compartir con aliados cercanos si tiene el conocimiento
                if "Cazar en grupo" in self.knowledge:
                    allies = world.get_nearby_nexus(self.position, 100, exclude=self)
                    if allies:
                        # Dividir parte de la energía con los aliados
                        share_energy = energy_gained * 0.3  # 30% para compartir
                        energy_gained -= share_energy
                        energy_per_ally = share_energy / len(allies)

                        for ally in allies:
                            if ally.alive:
                                ally.energy = min(100, ally.energy + energy_per_ally)
                                ally.add_memory(f"Recibió comida compartida de {self.name}")
            else:
                # Caza fallida
                memory_text = f"Intentó cazar un {source.type} pero falló"
                # El animal huye
                source.fear_level = 1.0

        elif source_type == "water" and source.fish_population > 0:
            # Pescar
            fishing_success = self.attributes["intelligence"] * 0.6

            if "Pescar" in self.knowledge:
                fishing_success += 0.3

            if random.random() < fishing_success:
                energy_gained = source.fish()
                memory_text = "Pescó un pez con éxito"

                # Aprender de la experiencia
                self.attributes["intelligence"] = min(1.0, self.attributes["intelligence"] + 0.01)
            else:
                memory_text = "Intentó pescar pero no tuvo éxito"

        # Aplicar energía ganada
        if energy_gained > 0:
            self.energy = min(100, self.energy + energy_gained)
            self.add_memory(memory_text)

            # Posibilidad de descubrir conocimiento relacionado
            if random.random() < 0.05 * self.attributes["intelligence"]:
                if source_type == "tree" and "Recolectar bayas" not in self.knowledge:
                    self.learn("Recolectar bayas")
                elif source_type == "animal" and "Cazar en grupo" not in self.knowledge:
                    self.learn("Cazar en grupo")
                elif source_type == "water" and "Pescar" not in self.knowledge:
                    self.learn("Pescar")

    def socialize_with(self, other_nexus):
        """Socializa con otro Nexus.

        Args:
            other_nexus (Nexus): Otro Nexus con el que socializar.
        """
        # Moverse hacia el otro Nexus
        direction_x = other_nexus.position[0] - self.position[0]
        direction_y = other_nexus.position[1] - self.position[1]

        # Calcular distancia
        distance = math.sqrt(direction_x**2 + direction_y**2)

        # Si está lo suficientemente cerca, interactuar
        if distance < self.size + other_nexus.size + 10:
            # Inicializar relación si no existe
            if other_nexus.name not in self.relationships:
                self.relationships[other_nexus.name] = {"value": 0.5, "type": "acquaintance"}

            # Mejorar relación
            relationship_change = 0.01 * (self.personality["sociability"] + other_nexus.personality["sociability"]) / 2
            self.relationships[other_nexus.name]["value"] = min(1.0, self.relationships[other_nexus.name]["value"] + relationship_change)

            # Posibilidad de compartir conocimientos
            if random.random() < 0.1 and hasattr(self, "knowledge") and other_nexus.alive and hasattr(other_nexus, "knowledge"):
                if self.knowledge:  # Verificar que tenga conocimientos para compartir
                    knowledge_to_share = random.choice(list(self.knowledge))
                    if knowledge_to_share not in other_nexus.knowledge:
                        other_nexus.learn(knowledge_to_share)
                        other_nexus.add_memory(f"Aprendió {knowledge_to_share} de {self.name}")
                        self.add_memory(f"Enseñó {knowledge_to_share} a {other_nexus.name}")

            # Reducir velocidad cuando está socializando
            self.velocity = [self.velocity[0] * 0.5, self.velocity[1] * 0.5]
            return

        # Si está lejos, moverse hacia el otro Nexus
        # Normalizar el vector
        if distance > 0:
            direction_x /= distance
            direction_y /= distance

        # Establecer velocidad (más lento cuando socializa)
        speed_multiplier = 0.7  # Más lento
        self.velocity[0] = direction_x * self.attributes["speed"] * speed_multiplier
        self.velocity[1] = direction_y * self.attributes["speed"] * speed_multiplier

    def seek_shelter(self, shelter):
        """Busca y se dirige hacia un refugio.

        Args:
            shelter: Refugio al que dirigirse.
        """
        # Calcular dirección hacia el refugio
        direction_x = shelter.position[0] - self.position[0]
        direction_y = shelter.position[1] - self.position[1]

        # Calcular distancia
        distance = math.sqrt(direction_x**2 + direction_y**2)

        # Si está muy cerca, entrar al refugio
        if distance < shelter.size:
            # Ya está en el refugio, descansar
            self.velocity = [0, 0]

            # Recuperar energía y salud más rápido
            self.energy = min(100, self.energy + 0.1 * shelter.comfort / 10)
            self.health = min(100, self.health + 0.05 * shelter.comfort / 10)

            # Añadir recuerdo
            if not hasattr(self, "last_shelter_memory") or self.last_shelter_memory < self.age - 100:
                self.add_memory(f"Descansó en el refugio de la tribu")
                self.last_shelter_memory = self.age

            return

        # Normalizar el vector
        if distance > 0:
            direction_x /= distance
            direction_y /= distance

        # Establecer velocidad
        self.velocity[0] = direction_x * self.attributes["speed"]
        self.velocity[1] = direction_y * self.attributes["speed"]

    def explore(self, world):
        """Explora el entorno de manera más natural y variada.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Inicializar atributos de exploración si no existen
        if not hasattr(self, "exploration_timer"):
            self.exploration_timer = 0
            self.exploration_target = None
            self.exploration_mode = "wander"  # Modos: "wander", "target", "rest"
            self.rest_timer = 0
            self.last_direction_change = 0

        # Incrementar temporizadores
        self.exploration_timer += 1
        self.last_direction_change += 1

        # Gestionar modos de exploración
        if self.exploration_mode == "wander":
            # Movimiento aleatorio con cambios graduales de dirección
            if self.last_direction_change > 30 or (self.velocity[0] == 0 and self.velocity[1] == 0):
                # Cambiar dirección de forma más natural (no completamente aleatoria)
                current_angle = math.atan2(self.velocity[1], self.velocity[0]) if (self.velocity[0] != 0 or self.velocity[1] != 0) else 0
                # Añadir una variación al ángulo actual (-45° a +45°)
                angle_variation = random.uniform(-math.pi/4, math.pi/4)
                new_angle = current_angle + angle_variation

                # Aplicar nueva dirección con velocidad variable
                speed_variation = random.uniform(0.7, 1.0)
                self.velocity[0] = math.cos(new_angle) * self.attributes["speed"] * speed_variation
                self.velocity[1] = math.sin(new_angle) * self.attributes["speed"] * speed_variation

                self.last_direction_change = 0

            # Posibilidad de cambiar a modo objetivo o descanso
            if self.exploration_timer > 100:
                choice = random.random()
                if choice < 0.6:  # 60% probabilidad de elegir un objetivo
                    # Buscar un punto interesante en la distancia
                    distance = random.uniform(100, 300)
                    angle = random.uniform(0, 2 * math.pi)
                    target_x = self.position[0] + math.cos(angle) * distance
                    target_y = self.position[1] + math.sin(angle) * distance

                    # Verificar que el objetivo esté dentro del mundo
                    if hasattr(world, 'is_in_world') and world.is_in_world((target_x, target_y)):
                        self.exploration_target = (target_x, target_y)
                        self.exploration_mode = "target"
                        self.add_memory("Decidió explorar hacia un área interesante en la distancia")
                elif choice < 0.8:  # 20% probabilidad de descansar
                    self.exploration_mode = "rest"
                    self.rest_timer = 0
                    self.velocity = [0, 0]
                    self.add_memory("Decidió descansar un momento durante su exploración")

                self.exploration_timer = 0

        elif self.exploration_mode == "target":
            # Moverse hacia un objetivo específico
            if self.exploration_target:
                # Calcular dirección hacia el objetivo
                direction_x = self.exploration_target[0] - self.position[0]
                direction_y = self.exploration_target[1] - self.position[1]
                distance = math.sqrt(direction_x**2 + direction_y**2)

                # Si llegó al objetivo o está cerca, volver a modo vagabundeo
                if distance < 20:
                    self.exploration_mode = "wander"
                    self.exploration_timer = 0
                    self.add_memory("Llegó al área que quería explorar")
                else:
                    # Normalizar dirección
                    if distance > 0:
                        direction_x /= distance
                        direction_y /= distance

                    # Añadir pequeña variación para movimiento más natural
                    if self.last_direction_change > 15:
                        angle_variation = random.uniform(-0.2, 0.2)
                        cos_var = math.cos(angle_variation)
                        sin_var = math.sin(angle_variation)
                        new_dir_x = direction_x * cos_var - direction_y * sin_var
                        new_dir_y = direction_x * sin_var + direction_y * cos_var
                        direction_x, direction_y = new_dir_x, new_dir_y
                        self.last_direction_change = 0

                    # Establecer velocidad con variación
                    speed_variation = random.uniform(0.8, 1.0)
                    self.velocity[0] = direction_x * self.attributes["speed"] * speed_variation
                    self.velocity[1] = direction_y * self.attributes["speed"] * speed_variation
            else:
                # Si no hay objetivo, volver a modo vagabundeo
                self.exploration_mode = "wander"

        elif self.exploration_mode == "rest":
            # Permanecer quieto durante un tiempo
            self.rest_timer += 1
            self.velocity = [0, 0]

            # Recuperar energía mientras descansa
            self.energy = min(100, self.energy + 0.05)

            # Terminar descanso después de un tiempo
            if self.rest_timer > 50:  # ~50 ticks de descanso
                self.exploration_mode = "wander"
                self.exploration_timer = 0
                self.add_memory("Terminó su descanso y continuó explorando")

        # Posibilidad de descubrir algo mientras explora (mayor si está en modo objetivo)
        discovery_chance = 0.01 * self.attributes["curiosity"]
        if self.exploration_mode == "target":
            discovery_chance *= 2  # Doble probabilidad en áreas de interés

        if random.random() < discovery_chance:
            self.discover_something(world)

        # Buscar refugio cercano si está cansado o es de noche
        if (self.energy < 30 or (hasattr(world, "time_of_day") and world.time_of_day == "night")) and hasattr(world, "shelter_system"):
            nearest_shelter = world.shelter_system.get_nearest_shelter(self.position, self.tribe)
            if nearest_shelter and random.random() < 0.3:
                self.exploration_target = nearest_shelter.position
                self.exploration_mode = "target"
                self.add_memory(f"Decidió buscar refugio en {nearest_shelter.name} para descansar")

    def discover_something(self, world):
        """Descubre algo nuevo durante la exploración (conocimiento, creencia o tradición).

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Determinar qué tipo de descubrimiento (conocimiento, creencia o tradición)
        discovery_type = random.choices(
            ["knowledge", "belief", "tradition"],
            weights=[0.6, 0.3, 0.1],
            k=1
        )[0]

        if discovery_type == "knowledge":
            self.discover_knowledge(world)
        elif discovery_type == "belief" and hasattr(world, "belief_system") and world.belief_system:
            self.discover_belief(world)
        elif discovery_type == "tradition" and hasattr(world, "belief_system") and world.belief_system:
            self.discover_tradition(world)

    def discover_knowledge(self, world):
        """Descubre un nuevo conocimiento durante la exploración.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Verificar si hay conocimientos disponibles para descubrir
        if not world.available_knowledge:
            return

        # Probabilidad basada en inteligencia
        if random.random() < self.attributes["intelligence"] * 0.5:
            # Seleccionar un conocimiento aleatorio
            new_knowledge = random.choice(world.available_knowledge)

            # Añadir a los conocimientos del Nexus
            if new_knowledge not in self.knowledge:
                self.knowledge.add(new_knowledge)

                # Añadir recuerdo
                self.add_memory(f"Descubrió {new_knowledge}")

                # Aumentar felicidad
                self.emotions["happiness"] = min(1.0, self.emotions["happiness"] + 0.2)

    def discover_belief(self, world):
        """Descubre una nueva creencia basada en la observación del entorno.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Verificar si hay creencias disponibles
        if not world.belief_system.natural_beliefs:
            return

        # Probabilidad basada en inteligencia y curiosidad
        if random.random() < (self.attributes["intelligence"] + self.attributes["curiosity"]) * 0.3:
            # Seleccionar una creencia aleatoria que no tenga ya
            available_beliefs = [b for b in world.belief_system.natural_beliefs
                               if not hasattr(self, "beliefs") or b not in self.beliefs]

            if available_beliefs:
                new_belief = random.choice(available_beliefs)

                # Añadir a las creencias del Nexus
                if not hasattr(self, "beliefs"):
                    self.beliefs = []

                self.beliefs.append(new_belief)
                new_belief.followers += 1

                # Añadir recuerdo
                self.add_memory(f"Desarrolló la creencia '{new_belief.name}'")

                # Aumentar felicidad
                self.emotions["happiness"] = min(1.0, self.emotions["happiness"] + 0.15)

                # Posibilidad de compartir con tribu
                if self.tribe and random.random() < self.personality["sociability"] * 0.5:
                    for member in self.tribe.members:
                        if member != self and member.alive and random.random() < 0.3:
                            if not hasattr(member, "beliefs"):
                                member.beliefs = []
                            if new_belief not in member.beliefs:
                                member.beliefs.append(new_belief)
                                new_belief.followers += 1
                                member.add_memory(f"Adoptó la creencia '{new_belief.name}' de {self.name}")

    def discover_tradition(self, world):
        """Inicia una nueva tradición cultural.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Solo si pertenece a una tribu y tiene suficiente inteligencia
        if not self.tribe or self.attributes["intelligence"] < 0.6:
            return

        # Probabilidad basada en inteligencia y sociabilidad
        if random.random() < (self.attributes["intelligence"] + self.personality["sociability"]) * 0.2:
            # Generar nombre y descripción
            tradition_types = ["Celebración", "Ritual", "Ceremonia", "Costumbre", "Práctica"]
            tradition_themes = ["estacional", "de iniciación", "de agradecimiento", "de protección", "de unidad"]
            name = f"{random.choice(tradition_types)} {random.choice(tradition_themes)}"

            descriptions = [
                "Tradición que fortalece los lazos comunitarios a través de actividades compartidas.",
                "Práctica cultural que honra a los ancestros y preserva la memoria colectiva.",
                "Costumbre que marca transiciones importantes en la vida de los miembros de la tribu.",
                "Ceremonia que celebra la relación de la tribu con su entorno natural.",
                "Ritual que refuerza la identidad cultural y el sentido de pertenencia."
            ]
            description = random.choice(descriptions)

            # Crear la nueva tradición
            from society.beliefs import CulturalTradition
            new_tradition = CulturalTradition(name, description, world.time, self.tribe)

            # Asociar con creencias existentes del Nexus
            if hasattr(self, "beliefs"):
                for belief in self.beliefs:
                    if random.random() < 0.5:
                        new_tradition.add_belief(belief)

            # Buscar rituales existentes para asociar
            from society.culture import CultureSystem
            if hasattr(world, "culture_system") and isinstance(world.culture_system, CultureSystem):
                for ritual in world.culture_system.rituals:
                    if random.random() < 0.2:
                        new_tradition.add_ritual(ritual)

            # Añadir a la lista de tradiciones
            world.belief_system.traditions.append(new_tradition)
            self.tribe.traditions = getattr(self.tribe, "traditions", []) + [new_tradition]

            # Notificar a los miembros de la tribu
            for member in self.tribe.members:
                if member.alive:
                    member.add_memory(f"Su tribu adoptó la tradición '{new_tradition.name}' iniciada por {self.name}")

            # Aumentar felicidad y estatus social
            self.emotions["happiness"] = min(1.0, self.emotions["happiness"] + 0.3)
            self.social_status = min(1.0, self.social_status + 0.1)

    def handle_reproduction(self, world):
        """Gestiona la reproducción del Nexus.

        Args:
            world (World): Referencia al mundo de la simulación.
        """
        # Verificar si está en edad reproductiva
        if self.age < self.sexual_maturity_age:
            return

        # Verificar si tiene suficiente energía para reproducirse
        if self.energy < world.config.NEXUS_REPRODUCTION_ENERGY_THRESHOLD:
            return

        # Si está embarazada, avanzar gestación
        if self.is_pregnant:
            self.gestation_counter += 1

            # Si ha completado el período de gestación, dar a luz
            if self.gestation_counter >= self.gestation_time:
                # Crear nuevo Nexus
                for _ in range(self.offspring_to_generate):
                    # Generar nombre para el hijo
                    child_name = f"Hijo de {self.name}"

                    # Crear nuevo Nexus con herencia de atributos
                    child = Nexus(
                        name=child_name,
                        position=(self.position[0] + random.uniform(-20, 20),
                                 self.position[1] + random.uniform(-20, 20)),
                        parent1=self,
                        parent2=self.pregnancy_partner
                    )

                    # Añadir al mundo
                    world.add_entity(child)

                    # Añadir recuerdo
                    self.add_memory(f"Dio a luz a {child_name}")

                # Reiniciar estado de embarazo
                self.is_pregnant = False
                self.gestation_counter = 0
                self.pregnancy_partner = None
                self.offspring_to_generate = 0

                # Consumir energía por el parto
                self.energy = max(0, self.energy - 30)

            return

        # Probabilidad base de reproducción
        reproduction_chance = 0.001  # 0.1% por tick

        # Aumentar probabilidad si hay pocos Nexus en el mundo
        if len([e for e in world.entities if e.alive]) < 5:
            reproduction_chance *= 5

        # Intentar reproducción
        if random.random() < reproduction_chance:
            # Reproducción asexual (simplificada)
            if self.reproduction_mode == "asexual":
                self.is_pregnant = True
                self.offspring_to_generate = 1
                self.add_memory("Comenzó proceso de reproducción asexual")

            # Reproducción sexual
            else:
                # Buscar pareja
                potential_mate = world.find_potential_mate(self)

                if potential_mate:
                    # Establecer embarazo
                    self.is_pregnant = True
                    self.pregnancy_partner = potential_mate
                    self.offspring_to_generate = random.randint(1, 2)  # 1-2 hijos

                    # Añadir recuerdos
                    self.add_memory(f"Se apareó con {potential_mate.name}")
                    potential_mate.add_memory(f"Se apareó con {self.name}")

                    # Consumir energía
                    self.energy = max(0, self.energy - 20)
                    potential_mate.energy = max(0, potential_mate.energy - 10)

                    # Establecer relación si no existe
                    if potential_mate.name not in self.relationships:
                        self.relationships[potential_mate.name] = {"value": 0.5, "type": "mate"}

                    if self.name not in potential_mate.relationships:
                        potential_mate.relationships[self.name] = {"value": 0.5, "type": "mate"}

                    # Mejorar relación
                    self.relationships[potential_mate.name]["value"] = min(1.0, self.relationships[potential_mate.name]["value"] + 0.2)
                    potential_mate.relationships[self.name]["value"] = min(1.0, potential_mate.relationships[self.name]["value"] + 0.2)

    def add_memory(self, memory_text):
        """Añade un recuerdo a la memoria del Nexus.

        Args:
            memory_text (str): Texto del recuerdo.
        """
        # Limitar el tamaño de la memoria
        if len(self.memory) >= 20:
            self.memory.pop(0)  # Eliminar el recuerdo más antiguo

        # Crear un diccionario con el texto y el tiempo
        memory_entry = {"text": memory_text, "time": 0}
        if hasattr(self, "world") and self.world:
            memory_entry["time"] = self.world.time

        self.memory.append(memory_entry)

    def learn(self, knowledge):
        """Aprende un nuevo conocimiento.

        Args:
            knowledge (str): Conocimiento a aprender.
        """
        self.knowledge.add(knowledge)

    def die(self):
        """Gestiona la muerte del Nexus."""
        # Marcar como no vivo
        self.alive = False

        # Detener movimiento
        self.velocity = [0, 0]

        # Añadir recuerdo final si es posible
        if hasattr(self, "world") and self.world:
            cause = "vejez" if self.age > self.max_age else "falta de energía" if self.energy <= 0 else "heridas"
            self.add_memory(f"Murió por {cause} a la edad de {self.age}")

            # Notificar a Nexus cercanos (no solo de la tribu)
            nearby_nexus = self.world.get_nearby_nexus(self.position, 200, exclude=self)
            for nexus in nearby_nexus:
                if nexus.alive:
                    # Añadir recuerdo y reacción emocional
                    nexus.add_memory(f"Presenció la muerte de {self.name} por {cause}")

                    # Reacción emocional basada en la relación
                    relationship_value = 0.0
                    if self.name in nexus.relationships:
                        relationship_value = nexus.relationships[self.name]["value"]

                    # Aumentar tristeza/miedo según la relación
                    if relationship_value > 0.7:  # Relación cercana
                        nexus.emotions["happiness"] = max(0.0, nexus.emotions["happiness"] - 0.3)
                        # Huir si la causa fue un depredador
                        if cause == "heridas" and "depredador" in self.memory[-1]["text"].lower():
                            nexus.emotions["fear"] = min(1.0, nexus.emotions["fear"] + 0.4)
                            # Forzar huida
                            nexus.state = "fleeing"
                            # Alejarse de la posición del muerto
                            direction_x = nexus.position[0] - self.position[0]
                            direction_y = nexus.position[1] - self.position[1]
                            length = math.sqrt(direction_x**2 + direction_y**2)
                            if length > 0:
                                direction_x /= length
                                direction_y /= length
                                nexus.velocity[0] = direction_x * nexus.attributes["speed"] * 1.5
                                nexus.velocity[1] = direction_y * nexus.attributes["speed"] * 1.5
                    elif relationship_value > 0.3:  # Conocido
                        nexus.emotions["happiness"] = max(0.0, nexus.emotions["happiness"] - 0.1)

                    # Siempre aumentar miedo si la causa fue un depredador
                    if cause == "heridas" and "depredador" in self.memory[-1]["text"].lower():
                        nexus.emotions["fear"] = min(1.0, nexus.emotions["fear"] + 0.2)

            # Notificar a miembros de la tribu
            if self.tribe:
                for member in self.tribe.members:
                    if member != self and member.alive:
                        member.add_memory(f"{self.name} murió por {cause}")
                        # Reacción emocional más fuerte para miembros de la tribu
                        member.emotions["happiness"] = max(0.0, member.emotions["happiness"] - 0.2)

                # Si era líder de la tribu, elegir nuevo líder
                if hasattr(self.tribe, "leader") and self.tribe.leader == self:
                    alive_members = [m for m in self.tribe.members if m.alive and m != self]
                    if alive_members:
                        # Elegir al miembro con mayor estatus social o inteligencia
                        new_leader = max(alive_members, key=lambda m: m.social_status + m.attributes["intelligence"])
                        self.tribe.leader = new_leader

                        # Notificar a todos los miembros
                        for member in self.tribe.members:
                            if member.alive:
                                member.add_memory(f"{new_leader.name} se convirtió en el nuevo líder de la tribu")

        # Cambiar color a gris (muerto)
        self.color = (150, 150, 150)

        # Añadir al sistema de descomposición si existe
        if hasattr(self, "world") and self.world and hasattr(self.world, "decomposition_system"):
            self.world.decomposition_system.add_corpse(self)

        # Manejar pérdida de conocimiento
        if hasattr(self, "world") and self.world and hasattr(self.world, "learning_system"):
            lost_knowledge = self.world.learning_system.handle_knowledge_loss(self, self.world)

            # Notificar conocimientos perdidos
            if lost_knowledge:
                knowledge_str = ", ".join(lost_knowledge)
                self.world.add_event(f"¡Conocimientos perdidos con la muerte de {self.name}: {knowledge_str}!")

    def calculate_compatibility(self, other):
        """Calcula la compatibilidad con otro Nexus para reproducción.

        Args:
            other (Nexus): Otro Nexus.

        Returns:
            float: Valor de compatibilidad entre 0 y 1.
        """
        # Verificar sexo para reproducción sexual
        if self.reproduction_mode == "sexual" and self.sex == other.sex:
            return 0.0

        # Calcular compatibilidad basada en atributos
        attribute_compatibility = 0
        for attr in self.attributes:
            if attr in other.attributes:
                # Mayor compatibilidad si los atributos son similares
                similarity = 1 - abs(self.attributes[attr] - other.attributes[attr])
                attribute_compatibility += similarity

        attribute_compatibility /= len(self.attributes)

        # Considerar relación existente
        relationship_factor = 0.5  # Neutral por defecto
        if other.name in self.relationships:
            relationship_factor = self.relationships[other.name]["value"]

        # Combinar factores
        return (attribute_compatibility * 0.7) + (relationship_factor * 0.3)

    def render(self, screen, camera_offset=(0, 0)):
        """Renderiza el Nexus en la pantalla.

        Args:
            screen (pygame.Surface): Superficie donde renderizar.
            camera_offset (tuple): Desplazamiento de la cámara (x, y).
        """
        import pygame

        # Calcular centro de la pantalla
        center_screen_x = screen.get_width() // 2
        center_screen_y = screen.get_height() // 2

        # Calcular posición en pantalla (relativa al centro)
        screen_x = int(center_screen_x + (self.position[0] - camera_offset[0]))
        screen_y = int(center_screen_y + (self.position[1] - camera_offset[1]))

        # No renderizar si está fuera de la pantalla
        if (screen_x < -self.size or screen_x > screen.get_width() + self.size or
            screen_y < -self.size or screen_y > screen.get_height() + self.size):
            return

        # Dibujar cuerpo
        pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.size)

        # Dibujar borde
        pygame.draw.circle(screen, (0, 0, 0), (screen_x, screen_y), self.size, 1)

        # Dibujar ojos
        eye_offset = 5
        pygame.draw.circle(screen, (255, 255, 255),
                          (screen_x - eye_offset, screen_y - eye_offset), 3)
        pygame.draw.circle(screen, (255, 255, 255),
                          (screen_x + eye_offset, screen_y - eye_offset), 3)

        # Pupilas
        pygame.draw.circle(screen, (0, 0, 0),
                          (screen_x - eye_offset, screen_y - eye_offset), 1)
        pygame.draw.circle(screen, (0, 0, 0),
                          (screen_x + eye_offset, screen_y - eye_offset), 1)

        # Dibujar boca según estado emocional
        if self.emotions["happiness"] > 0.7:
            # Sonrisa
            pygame.draw.arc(screen, (0, 0, 0),
                           (screen_x - 5, screen_y, 10, 5), 0, 3.14, 1)
        elif self.emotions["fear"] > 0.7:
            # Boca de miedo
            pygame.draw.circle(screen, (0, 0, 0),
                              (screen_x, screen_y + 5), 2)
        else:
            # Boca neutral
            pygame.draw.line(screen, (0, 0, 0),
                            (screen_x - 3, screen_y + 5),
                            (screen_x + 3, screen_y + 5), 1)

        # Indicador de estado
        if self.state == "seeking_food":
            # Círculo amarillo
            pygame.draw.circle(screen, (255, 255, 0),
                              (screen_x, screen_y - self.size - 5), 3)
        elif self.state == "fleeing":
            # Círculo rojo
            pygame.draw.circle(screen, (255, 0, 0),
                              (screen_x, screen_y - self.size - 5), 3)
        elif self.state == "resting":
            # Círculo azul
            pygame.draw.circle(screen, (0, 0, 255),
                              (screen_x, screen_y - self.size - 5), 3)

        # Indicador de salud
        health_width = 20
        health_height = 3
        health_x = screen_x - health_width // 2
        health_y = screen_y - self.size - 10

        # Fondo de la barra de salud
        pygame.draw.rect(screen, (100, 100, 100),
                        (health_x, health_y, health_width, health_height))

        # Barra de salud
        health_fill_width = int(health_width * (self.health / 100))
        health_color = (0, 255, 0)  # Verde
        if self.health < 30:
            health_color = (255, 0, 0)  # Rojo si está bajo de salud
        elif self.health < 70:
            health_color = (255, 255, 0)  # Amarillo si está medio

        pygame.draw.rect(screen, health_color,
                        (health_x, health_y, health_fill_width, health_height))

# Fin del archivo: bloque de comillas triples cerrado correctamente.